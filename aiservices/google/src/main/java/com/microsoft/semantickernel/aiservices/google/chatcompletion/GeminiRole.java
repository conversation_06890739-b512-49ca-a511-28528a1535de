// Copyright (c) Microsoft. All rights reserved.
package com.microsoft.semantickernel.aiservices.google.chatcompletion;

/**
 * Represents the role of a message in a Gemini conversation.
 */
public enum GeminiRole {
    /**
     * A user message is a message generated by the user.
     */
    USER("user"),
    /**
     * A model message is a message generated by the model.
     */
    MODEL("model");

    private final String role;

    private GeminiRole(String role) {
        this.role = role;
    }

    @Override
    public String toString() {
        return role;
    }
}
