// Copyright (c) Microsoft. All rights reserved.
package com.microsoft.semantickernel.aiservices.huggingface.models;

import com.azure.core.util.BinaryData;
import com.microsoft.semantickernel.aiservices.huggingface.models.ChatCompletionRequest.ChatMessage;
import com.microsoft.semantickernel.aiservices.huggingface.models.ChatCompletionRequest.ChatMessageFunction;
import com.microsoft.semantickernel.aiservices.huggingface.models.ChatCompletionRequest.ChatMessageToolCall;
import com.microsoft.semantickernel.services.chatcompletion.ChatPromptParseVisitor;
import com.microsoft.semantickernel.services.chatcompletion.ChatXMLPromptParser;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// TODO Support TGI
public class HuggingFaceXMLPromptParser {

    private static final Logger LOGGER = LoggerFactory.getLogger(HuggingFaceXMLPromptParser.class);

    public static class HuggingFaceParsedPrompt {

        private final List<ChatMessage> chatRequestMessages;
        private final List<ChatMessageToolCall> functions;

        protected HuggingFaceParsedPrompt(
            List<ChatMessage> parsedMessages,
            @Nullable List<ChatMessageToolCall> parsedFunctions
        ) {
            this.chatRequestMessages = parsedMessages;
            if (parsedFunctions == null) {
                parsedFunctions = new ArrayList<>();
            }
            this.functions = parsedFunctions;
        }

        public List<ChatMessage> getChatRequestMessages() {
            return chatRequestMessages;
        }

        public List<ChatMessageToolCall> getFunctions() {
            return functions;
        }
    }

    private static class HuggingFaceChatPromptParseVisitor implements
        ChatPromptParseVisitor<HuggingFaceParsedPrompt> {

        private HuggingFaceParsedPrompt parsedRaw;
        private final List<ChatMessageToolCall> functionDefinitions = new ArrayList<>();
        private final List<ChatMessage> messages = new ArrayList<>();

        @Override
        public ChatPromptParseVisitor<HuggingFaceParsedPrompt> addMessage(
            String role,
            String content) {
            messages.add(new ChatMessage(
                role,
                content,
                null,
                null));
            return this;
        }

        @Override
        public ChatPromptParseVisitor<HuggingFaceParsedPrompt> addFunction(
            String name,
            @Nullable
            String description,
            @Nullable
            BinaryData parameters) {

            String paramString = null;
            if (parameters != null) {
                paramString = parameters.toString();
            }

            ChatMessageToolCall function = new ChatMessageToolCall(
                name,
                null,
                new ChatMessageFunction(
                    description,
                    name,
                    paramString
                )
            );

            functionDefinitions.add(function);

            return this;
        }

        @Override
        public boolean areMessagesEmpty() {
            return messages.isEmpty();
        }

        @Override
        public ChatPromptParseVisitor<HuggingFaceParsedPrompt> fromRawPrompt(
            String rawPrompt) {

            ChatMessage message = new ChatMessage(
                "user",
                rawPrompt,
                null,
                null
            );

            this.parsedRaw = new HuggingFaceParsedPrompt(Collections.singletonList(message),
                null);

            return this;
        }

        @Override
        public HuggingFaceParsedPrompt get() {
            if (parsedRaw != null) {
                return parsedRaw;
            }

            return new HuggingFaceParsedPrompt(messages, functionDefinitions);
        }

        @Override
        public ChatPromptParseVisitor<HuggingFaceParsedPrompt> reset() {
            return new HuggingFaceChatPromptParseVisitor();
        }
    }

    public static HuggingFaceParsedPrompt parse(String rawPrompt) {
        ChatPromptParseVisitor<HuggingFaceParsedPrompt> visitor = ChatXMLPromptParser.parse(
            rawPrompt,
            new HuggingFaceChatPromptParseVisitor());

        return visitor.get();
    }
}