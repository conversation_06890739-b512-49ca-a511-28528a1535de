package com.microsoft.semantickernel.aiservices.huggingface.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import javax.annotation.Nullable;

// TODO Support TGI
public class ChatCompletionRequest {


    /// <summary>
    /// This is the default name when using TGI and will be ignored as the TGI will only target the current activated model.
    /// </summary>
    public static final String TextGenerationInferenceDefaultModel = "tgi";


    /// <summary>
    /// Model name to use for generation.
    /// </summary>
    /// <remarks>
    /// When using TGI this parameter will be ignored.
    /// </remarks>
    @Nullable
    public final String model;

    /// <summary>
    /// Indicates whether to get the response as stream or not.
    /// </summary>
    public final boolean stream;

    @Nullable
    public final List<ChatMessage> messages;

    /// <summary>
    /// Whether to return log probabilities of the output tokens or not. If true, returns the log probabilities of each
    /// output token returned in the content of message.
    /// </summary>
    @Nullable
    public final Boolean logprobs;

    /// <summary>
    /// An integer between 0 and 5 specifying the number of most likely tokens to return at each token position, each with
    /// an associated log probability. logprobs must be set to true if this parameter is used.
    /// </summary>
    @Nullable
    public final Integer topLogProbs;

    /// <summary>
    /// The maximum number of tokens that can be generated in the chat completion.
    /// </summary>
    @Nullable
    public final Integer maxTokens;

    /// <summary>
    /// Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far,
    /// increasing the model's likelihood to talk about new topics
    /// </summary>

    @Nullable
    public final Float presencePenalty;
    /// <summary>
    /// Up to 4 sequences where the API will stop generating further tokens.
    /// </summary>
    @Nullable
    public final List<String> stop;

    /// <summary>
    /// The seed to use for generating a similar output.
    /// </summary>
    @Nullable
    public final Long seed;

    /// <summary>
    /// What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while
    /// lower values like 0.2 will make it more focused and deterministic.
    ///
    /// We generally recommend altering this or `top_p` but not both.
    /// </summary>
    @Nullable
    public final Float temperature;

    /// <summary>
    /// An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the
    /// tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.
    /// </summary>
    @Nullable
    public final Float topP;

    public ChatCompletionRequest(
        @JsonProperty("model") String model,
        @JsonProperty("stream") boolean stream,
        @JsonProperty("messages") List<ChatMessage> messages,
        @JsonProperty("logprobs") Boolean logprobs,

        @Nullable
        @JsonProperty("top_logprobs") Integer topLogProbs,

        @Nullable
        @JsonProperty("max_tokens") Integer maxTokens,

        @Nullable
        @JsonProperty("presence_penalty") Float presencePenalty,
        @Nullable
        @JsonProperty("stop") List<String> stop,

        @Nullable
        @JsonProperty("seed") Long seed,
        @Nullable
        @JsonProperty("temperature") Float temperature,
        @Nullable
        @JsonProperty("top_p")
        Float topP
    ) {

        this.model = model;
        this.stream = stream;
        this.messages = messages;
        this.logprobs = logprobs;
        this.topLogProbs = topLogProbs;
        this.maxTokens = maxTokens;
        this.presencePenalty = presencePenalty;
        this.stop = stop;
        this.seed = seed;
        this.temperature = temperature;
        this.topP = topP;
    }

    public static class ChatMessageToolCall {

        @Nullable
        private final String id;

        @Nullable
        private final String type;

        private final ChatMessageFunction function;

        public ChatMessageToolCall(
            @Nullable
            @JsonProperty("id") String id,
            @Nullable
            @JsonProperty("type") String type,
            @Nullable
            @JsonProperty("function") ChatMessageFunction function
        ) {
            this.id = id;
            this.type = type;
            this.function = function;
        }
    }

    public static class ChatMessageFunction {

        @Nullable
        public final String description;
        @Nullable
        public final String name;
        @Nullable
        public final String parameters;

        public ChatMessageFunction(
            @JsonProperty("description") String description,
            @JsonProperty("name") String name,
            @JsonProperty("parameters") String parameters
        ) {
            this.description = description;
            this.name = name;
            this.parameters = parameters;
        }
    }

    public static class ChatMessage {

        @Nullable
        public final String role;
        @Nullable
        public final String content;
        @Nullable
        public final String name;
        @Nullable
        public final List<ChatMessageToolCall> toolCalls;


        public ChatMessage(
            @Nullable
            @JsonProperty("role") String role,
            @Nullable
            @JsonProperty("content") String content,
            @Nullable
            @JsonProperty("name") String name,
            @Nullable
            @JsonProperty("tool_calls") List<ChatMessageToolCall> toolCalls
        ) {
            this.role = role;
            this.content = content;
            this.name = name;
            this.toolCalls = toolCalls;
        }

    }
}
