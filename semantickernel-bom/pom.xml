<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.microsoft.semantic-kernel</groupId>
    <artifactId>semantickernel-bom</artifactId>
    <version>1.4.4-RC2-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>Semantic Kernel Java BOM</name>
    <description>Bill of Materials for all Semantic Kernel (Java) modules</description>
    <url>https://www.github.com/microsoft/semantic-kernel</url>

    <properties>
        <com.fasterxml.jackson.core.version>2.18.0</com.fasterxml.jackson.core.version>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.5.0</version>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>3.9.9</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>3.8.0</version>
            </plugin>
        </plugins>

    </build>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-bom</artifactId>
                <version>1.43.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-experimental</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-gpt3-tokenizer</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-plugin-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>aiservices-azureopenai</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-aiservices-huggingface</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-aiservices-openai</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-aiservices-google</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-data-azureaisearch</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-data-jdbc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-data-redis</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-agents-core</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-ai-openai</artifactId>
                <version>1.0.0-beta.14</version>
            </dependency>
            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-identity</artifactId>
                <version>1.14.0</version>
            </dependency>
            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-core</artifactId>
                <version>1.53.0</version>
            </dependency>
            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-search-documents</artifactId>
                <version>11.8.0-beta.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.azure</groupId>
                        <artifactId>azure-core-serializer-json-jackson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>5.2.0</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${com.fasterxml.jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${com.fasterxml.jackson.core.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${com.fasterxml.jackson.core.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${com.fasterxml.jackson.core.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- 4.4.0+ has as dependency on a higher version of Java than Java 8 -->
            <dependency>
                <groupId>com.github.jknack</groupId>
                <artifactId>handlebars</artifactId>
                <version>4.3.1</version>
            </dependency>
            <dependency>
                <groupId>jakarta.inject</groupId>
                <artifactId>jakarta.inject-api</artifactId>
                <version>2.0.1.MR</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>2.0.16</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>3.0.2</version>
            </dependency>

            <!-- Required for Android -->
            <dependency>
                <groupId>javax.xml.stream</groupId>
                <artifactId>stax-api</artifactId>
                <version>1.0-2</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-annotations</artifactId>
                <version>4.8.6</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.12.0</version>
            </dependency>

            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>google-cloud-vertexai</artifactId>
                <version>1.12.0</version>
            </dependency>

            <dependency>
                <groupId>com.github.victools</groupId>
                <artifactId>jsonschema-generator</artifactId>
                <version>4.36.0</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>com.github.victools</groupId>
                <artifactId>jsonschema-module-jackson</artifactId>
                <version>4.36.0</version>
                <optional>true</optional>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>release</id>

            <distributionManagement>
                <repository>
                    <id>${releaseRepoId}</id>
                    <!--suppress UnresolvedMavenProperty -->
                    <url>${releaseRepoUrl}</url>
                    <name>${releaseRepoName}</name>
                </repository>
            </distributionManagement>
        </profile>
        <profile>
            <id>github-packages</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <distributionManagement>
                <repository>
                    <id>github</id>
                    <name>GitHub Packages</name>
                    <url>https://maven.pkg.github.com/microsoft/semantic-kernel-java</url>
                </repository>
            </distributionManagement>
            <repositories>
                <repository>
                    <id>central</id>
                    <url>https://repo1.maven.org/maven2</url>
                </repository>
                <repository>
                    <id>github</id>
                    <name>GitHub Packages</name>
                    <url>https://maven.pkg.github.com/microsoft/semantic-kernel-java</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
        </profile>
    </profiles>

    <developers>
        <developer>
            <id>microsoft</id>
            <name>Microsoft</name>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>MIT License</name>
            <url>https://opensource.org/licenses/MIT</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <url>https://github.com/microsoft/semantic-kernel</url>
        <connection>scm:git:https://github.com/microsoft/semantic-kernel.git</connection>
        <developerConnection>scm:git:https://github.com/microsoft/semantic-kernel.git</developerConnection>
        <tag>HEAD</tag>
    </scm>
</project>
