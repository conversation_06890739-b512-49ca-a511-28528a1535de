// Copyright (c) Microsoft. All rights reserved.
package com.microsoft.semantickernel.data.jdbc;

import com.microsoft.semantickernel.data.vectorsearch.VectorSearchFilter;
import com.microsoft.semantickernel.data.filter.FilterMapping;

import java.util.List;

public interface SQLVectorStoreFilterQueryProvider extends FilterMapping {
    /**
     * Gets the filter parameters for the given vector search filter to associate with the filter string
     * generated by the getFilter method.
     *
     * @param filter The filter to get the filter parameters for.
     * @return The filter parameters.
     */
    List<Object> getFilterParameters(VectorSearchFilter filter);
}
