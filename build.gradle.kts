plugins {
	java
	id("org.springframework.boot") version "3.4.8"
	id("io.spring.dependency-management") version "1.1.7"
}

group = "com.phodal"
version = "0.0.1-SNAPSHOT"

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

repositories {
    mavenCentral()
    maven("https://repo.spring.io/milestone")
    maven("https://repo.spring.io/snapshot")
    maven {
        name = "Central Portal Snapshots"
        url = uri("https://central.sonatype.com/repository/maven-snapshots/")
    }
}

extra["springAiVersion"] = "1.0.1"

dependencies {
    implementation("org.springframework.boot:spring-boot-starter-web")

    implementation(platform("org.springframework.ai:spring-ai-bom:1.0.1"))
	implementation("org.springframework.ai:spring-ai-starter-model-openai")

    /// lombok
    implementation("org.projectlombok:lombok:1.18.28")

    // handlebars template engine
    implementation("com.github.jknack:handlebars:4.3.1")

    // java dataframe alternative
    implementation("tech.tablesaw:tablesaw-core:0.44.4")
    implementation("tech.tablesaw:tablesaw-json:0.44.4")
    implementation("org.springframework.boot:spring-boot-starter-actuator")

    testImplementation("org.springframework.boot:spring-boot-starter-test")
	testRuntimeOnly("org.junit.platform:junit-platform-launcher")
}

dependencyManagement {
	imports {
		mavenBom("org.springframework.ai:spring-ai-bom:${property("springAiVersion")}")
	}
}

tasks.withType<Test> {
	useJUnitPlatform()
}
