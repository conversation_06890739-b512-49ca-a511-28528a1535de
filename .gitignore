# VS Code files for those working on multiple tools
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Local History for Visual Studio Code
.history/

# JetBrains IntelliJ
.idea
*.ipr
*.iml
*.iws

# Maven settings
conf.properties
java/**/target
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Eclipse m2e generated files
# Eclipse Core
.project
# JDT-specific (Eclipse Java Development Tools)
.classpath

# Other
.env
certs/
launchSettings.json
config.development.yaml
*.development.config
*.development.json
.DS_Store
node_modules/
obj/
bin/
_dev/
.dev/
*.devis.*
.vs/
*.user
**/.vscode/chrome
**/.vscode/.ropeproject/objectdb
*.pyc
.ipynb_checkpoints
.jython_cache/
__pycache__/
.mypy_cache/
__pypackages__/
.pdm.toml
global.json
.java-version
