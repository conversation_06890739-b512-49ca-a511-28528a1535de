<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.microsoft.semantic-kernel</groupId>
    <artifactId>semantickernel-parent</artifactId>
    <version>1.4.4-RC2-SNAPSHOT</version>
    <packaging>pom</packaging>

    <url>https://www.github.com/microsoft/semantic-kernel</url>
    <name>Semantic Kernel Parent</name>
    <description>Parent pom for the Semantic Kernel Project</description>

    <properties>
        <azure-ai-openai.version>1.0.0-beta.14</azure-ai-openai.version>
        <checkstyle.version>10.18.2</checkstyle.version>
        <com.uber.nullaway.version>0.10.21</com.uber.nullaway.version>
        <compilingJdk8>false</compilingJdk8>
        <google.errorprone.core.version>2.19.1</google.errorprone.core.version>
        <google.java.format.version>1.17.0</google.java.format.version>
        <jreleaser.plugin.version>1.6.0</jreleaser.plugin.version>
        <junit5.version>5.11.3</junit5.version>
        <log4j2.version>2.24.1</log4j2.version>
        <maven.antrun-plugin.version>3.1.0</maven.antrun-plugin.version>
        <maven.changes-plugin.version>2.12.1</maven.changes-plugin.version>
        <maven.checkstyle-plugin.version>3.5.0</maven.checkstyle-plugin.version>
        <maven.clean-plugin.version>3.4.0</maven.clean-plugin.version>
        <maven.compiler-plugin.version>3.13.0</maven.compiler-plugin.version>
        <maven.compiler.release>8</maven.compiler.release>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven.dependency-plugin.version>3.8.0</maven.dependency-plugin.version>
        <maven.deploy-plugin.version>3.1.3</maven.deploy-plugin.version>
        <maven.directory-maven-plugin.version>1.0</maven.directory-maven-plugin.version>
        <maven.enforcer-plugin.version>3.5.0</maven.enforcer-plugin.version>
        <maven.exec-plugin.version>3.5.0</maven.exec-plugin.version>
        <maven.install-plugin.version>3.1.3</maven.install-plugin.version>
        <maven.jacoco-plugin.version>0.8.12</maven.jacoco-plugin.version>
        <maven.jar-plugin.version>3.4.2</maven.jar-plugin.version>
        <maven.javadoc-plugin.version>3.10.1</maven.javadoc-plugin.version>
        <maven.jxr-plugin.version>3.5.0</maven.jxr-plugin.version>
        <maven.license-plugin.version>2.4.0</maven.license-plugin.version>
        <!-- TODO move to 3.23.0+ in the future when moving PMD versions -->
        <maven.pmd-plugin.version>3.21.2</maven.pmd-plugin.version>
        <maven.project-info-reports-plugin.version>3.8.0</maven.project-info-reports-plugin.version>
        <maven.rat-plugin.version>0.16.1</maven.rat-plugin.version>
        <maven.release-plugin.version>3.1.1</maven.release-plugin.version>
        <maven.resources-plugin.version>3.3.1</maven.resources-plugin.version>
        <maven.site-plugin.version>4.0.0-M16</maven.site-plugin.version>
        <maven.shade-plugin.version>3.6.0</maven.shade-plugin.version>
        <maven.source-plugin.version>3.3.1</maven.source-plugin.version>
        <maven.spotbugs-plugin.version>4.8.6.5</maven.spotbugs-plugin.version>
        <!-- TODO 2.30.0 is the last supported version for Java 8 Not sure how 2.38.0 is working... -->
        <maven.spotless-plugin.version>2.38.0</maven.spotless-plugin.version>
        <maven.surefire-plugin.version>3.5.1</maven.surefire-plugin.version>
        <maven.version>3.9.9</maven.version>
        <maven.versions-plugin.version>2.17.1</maven.versions-plugin.version>
        <mockito.version>5.14.2</mockito.version>
        <mutability.detector.version>0.9.1</mutability.detector.version>
        <!-- TODO move to 7.6.0+ in the future -->
        <pmd.version>7.10.0</pmd.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.github.repository>microsoft/semantic-kernel</project.github.repository>
        <repository.url>**************:${project.github.repository}.git</repository.url>
        <spotbugs.version>4.8.6</spotbugs.version>
    </properties>

    <modules>
        <module>semantickernel-bom</module>
        <module>semantickernel-api</module>
        <module>semantickernel-experimental</module>
        <module>aiservices/openai</module>
        <module>aiservices/google</module>
        <module>aiservices/huggingface</module>
        <module>data/semantickernel-data-azureaisearch</module>
        <module>data/semantickernel-data-jdbc</module>
        <module>data/semantickernel-data-redis</module>
        <module>agents/semantickernel-agents-core</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-bom</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit5.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit5.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j2.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j2.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j2-impl</artifactId>
                <version>${log4j2.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-ai-openai</artifactId>
                <version>${azure-ai-openai.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-connectors-ai-openai</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel.extensions</groupId>
                <artifactId>semantickernel-sequentialplanner-extension</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel.extensions</groupId>
                <artifactId>semantickernel-actionplanner-extension</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-annotations</artifactId>
                <version>${spotbugs.version}</version>
            </dependency>

            <dependency>
                <groupId>org.wiremock</groupId>
                <artifactId>wiremock</artifactId>
                <version>3.9.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>5.14.2</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <!-- 
            NOTE: Some plugin configuration is set in the reporting section below as 
            opposed to here by design -->
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.github.spotbugs</groupId>
                    <artifactId>spotbugs-maven-plugin</artifactId>
                    <version>${maven.spotbugs-plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.github.spotbugs</groupId>
                            <artifactId>spotbugs</artifactId>
                            <version>${spotbugs.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-changes-plugin</artifactId>
                    <version>${maven.changes-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${maven.checkstyle-plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.puppycrawl.tools</groupId>
                            <artifactId>checkstyle</artifactId>
                            <version>${checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>${maven.clean-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven.compiler-plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.release}</source>
                        <target>${maven.compiler.release}</target>
                        <release>8</release>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${maven.dependency-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven.deploy-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>${maven.enforcer-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>enforce-maven</id>
                            <goals>
                                <goal>enforce</goal>
                            </goals>
                            <configuration>
                                <rules>
                                    <requireMavenVersion>
                                        <version>${maven.version}</version>
                                    </requireMavenVersion>
                                </rules>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${maven.install-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven.jar-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven.javadoc-plugin.version}</version>
                    <configuration>
                        <!-- TODO: Tighten this up when we go GA -->
                        <failOnError>false</failOnError>
                        <nohelp>true</nohelp>
                        <show>public</show>
                        <excludePackageNames>*.implementation.*:*.implementation</excludePackageNames>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jxr-plugin</artifactId>
                    <version>${maven.jxr-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-pmd-plugin</artifactId>
                    <version>${maven.pmd-plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>net.sourceforge.pmd</groupId>
                            <artifactId>pmd-core</artifactId>
                            <version>${pmd.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>net.sourceforge.pmd</groupId>
                            <artifactId>pmd-java</artifactId>
                            <version>${pmd.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>net.sourceforge.pmd</groupId>
                            <artifactId>pmd-javascript</artifactId>
                            <version>${pmd.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>net.sourceforge.pmd</groupId>
                            <artifactId>pmd-jsp</artifactId>
                            <version>${pmd.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>${maven.release-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven.shade-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven.source-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven.surefire-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>${maven.site-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-project-info-reports-plugin</artifactId>
                    <version>${maven.project-info-reports-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven.resources-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven.antrun-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.rat</groupId>
                    <artifactId>apache-rat-plugin</artifactId>
                    <version>${maven.rat-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>${maven.exec-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <version>${maven.license-plugin.version}</version>
                    <configuration>
                        <organizationName>Microsoft</organizationName>
                        <inceptionYear>2023</inceptionYear>
                        <licenseName>mit</licenseName>
                        <licenseFile>${basedir}/target/LICENSE</licenseFile>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>${maven.versions-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.commonjava.maven.plugins</groupId>
                    <artifactId>directory-maven-plugin</artifactId>
                    <version>${maven.directory-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${maven.jacoco-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>report</id>
                            <phase>prepare-package</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>animal-sniffer-maven-plugin</artifactId>
                    <version>1.24</version>
                    <executions>
                        <execution>
                            <id>android</id>
                            <phase>test</phase>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <!-- 
                            COMPATIBLE: Android API level 26 (Android 8.0)
                            See https://developer.android.com/studio/releases/platforms

                            Note: anything below this requires changes to the codebase to pass compatibility
                        -->
                        <signature>
                            <groupId>net.sf.androidscents.signature</groupId>
                            <artifactId>android-api-level-26</artifactId>
                            <version>8.0.0_r2</version>
                        </signature>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>license-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>update-project-license</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>add-third-party</id>
                        <goals>
                            <goal>add-third-party</goal>
                        </goals>
                        <configuration>
                            <includedScopes>compile,runtime</includedScopes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <!-- 
        If versions are not explicitly set it means they're inheriting from the 
        pluginManagement section above
    -->
    <reporting>
        <plugins>
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <configuration>
                    <plugins>
                        <plugin>
                            <groupId>org.mutabilitydetector</groupId>
                            <artifactId>MutabilityDetector4FindBugs</artifactId>
                            <version>${mutability.detector.version}</version>
                        </plugin>
                    </plugins>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-changes-plugin</artifactId>
                <!-- TODO: This configuration is only for when we produce GitHub published reports -->
                <!--
                <configuration>
                    <githubAPIScheme>https</githubAPIScheme>
                    <githubAPIPort>443</githubAPIPort>
                    <includeOpenIssues>false</includeOpenIssues>
                    <onlyCurrentVersion>false</onlyCurrentVersion>
                </configuration>
                -->
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>changes-report</report>
                            <!-- TODO: Might be able to generate a report based off GH issues -->
                            <!-- <report>github-report</report> -->
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>checkstyle</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <configuration>
                    <targetJdk>${maven.compiler.release}</targetJdk>
                    <!-- TODO: Add a rule set -->
                    <!--
                    <rulesets>
                        <ruleset>pmd-ruleset.xml</ruleset>
                    </rulesets>
                    -->
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.rat</groupId>
                <artifactId>apache-rat-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>license-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>dependency-updates-report</report>
                            <report>plugin-updates-report</report>
                            <report>property-updates-report</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${maven.jacoco-plugin.version}</version>
            </plugin>
        </plugins>
    </reporting>

    <repositories>
        <repository>
            <id>central</id>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <url>https://repo1.maven.org/maven2/</url>
        </repository>
    </repositories>

    <profiles>
        <profile>
            <id>compile-jdk8</id>
            <properties>
                <maven.compiler.source>1.8</maven.compiler.source>
                <maven.compiler.target>1.8</maven.compiler.target>
                <compilingJdk8>true</compilingJdk8>
            </properties>
        </profile>
        <profile>
            <!-- IF YOU CHANGE THIS FROM 17 GO UPDATE JDK_COMPILER_VERSION ON THE sk-java MAVEN DEPLOY -->
            <id>compile-jdk17</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <maven.compiler.source>17</maven.compiler.source>
                <maven.compiler.target>17</maven.compiler.target>
            </properties>
        </profile>
        <profile>
            <id>bug-check</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <version>${maven.compiler-plugin.version}</version>
                        <configuration>
                            <source>${maven.compiler.release}</source>
                            <target>${maven.compiler.release}</target>
                            <!-- Ensure we compile against JDK 8 APIs -->
                            <release>8</release>
                            <encoding>${project.build.sourceEncoding}</encoding>
                            <showWarnings>true</showWarnings>
                            <compilerArgs>
                                <arg>-XDcompilePolicy=simple</arg>

                                <!-- TODO: Remove exclusions when more project is stable -->
                                <arg>-Xplugin:ErrorProne
                                    -XepOpt:NullAway:AnnotatedPackages=com.microsoft.semantickernel
                                    -Xep:AlmostJavadoc:OFF -Xep:MissingSummary:OFF
                                    -Xep:UnusedVariable:OFF -Xep:EmptyBlockTag:OFF
                                    -XepExcludedPaths:.*/src/test/java/.*
                                </arg>
                            </compilerArgs>
                            <annotationProcessorPaths>
                                <path>
                                    <groupId>com.google.errorprone</groupId>
                                    <artifactId>error_prone_core</artifactId>
                                    <version>${google.errorprone.core.version}</version>
                                </path>
                                <path>
                                    <groupId>com.uber.nullaway</groupId>
                                    <artifactId>nullaway</artifactId>
                                    <version>${com.uber.nullaway.version}</version>
                                </path>
                            </annotationProcessorPaths>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>com.diffplug.spotless</groupId>
                        <artifactId>spotless-maven-plugin</artifactId>
                        <version>${maven.spotless-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>check</id>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <phase>compile</phase>
                            </execution>
                            <execution>
                                <id>apply</id>
                                <goals>
                                    <goal>apply</goal>
                                </goals>
                                <phase>process-sources</phase>
                            </execution>
                        </executions>
                        <configuration>
                            <skip>${compilingJdk8}</skip>
                            <java>
                                <eclipse>
                                    <version>4.30</version>
                                    <file>./eclipse-formatter.xml</file>
                                </eclipse>
                                <!--
                                <googleJavaFormat>
                                    <version>${google.java.format.version}</version>
                                    <style>AOSP</style>
                                    <reflowLongStrings>true</reflowLongStrings>
                                </googleJavaFormat>
                                -->
                                <licenseHeader>
                                    <content>// Copyright (c) Microsoft. All rights reserved.</content>
                                </licenseHeader>
                                <toggleOffOn />
                            </java>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>com.github.spotbugs</groupId>
                        <artifactId>spotbugs-maven-plugin</artifactId>
                        <version>${maven.spotbugs-plugin.version}</version>
                        <configuration>
                            <effort>Max</effort>
                            <!-- Lower when more project is stable -->
                            <threshold>Normal</threshold>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>spotbugs</goal>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- Check compatibility with Android API -->
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>animal-sniffer-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>with-samples</id>
            <modules>
                <module>samples</module>
                <module>api-test</module>
            </modules>
        </profile>
        <profile>
            <id>release</id>
            <properties>
                <maven.compiler.source>1.8</maven.compiler.source>
                <maven.compiler.target>1.8</maven.compiler.target>
            </properties>

            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-deploy-plugin</artifactId>
                        <configuration>
                            <javadoc>true</javadoc>
                            <sources>true</sources>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-release-plugin</artifactId>
                        <configuration>
                            <tagNameFormat>java-@{project.version}</tagNameFormat>
                        </configuration>
                    </plugin>
                </plugins>
            </build>

            <reporting>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                    </plugin>
                </plugins>
            </reporting>

            <repositories>
                <repository>
                    <id>${releaseRepoId}</id>
                    <url>${releaseRepoUrl}</url>
                    <name>${releaseRepoName}</name>
                </repository>
            </repositories>
            <distributionManagement>
                <repository>
                    <id>${releaseRepoId}</id>
                    <url>${releaseRepoUrl}</url>
                    <name>${releaseRepoName}</name>
                </repository>
            </distributionManagement>
        </profile>
        <profile>
            <id>github-packages</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <distributionManagement>
                <repository>
                    <id>github</id>
                    <name>GitHub Packages</name>
                    <url>https://maven.pkg.github.com/microsoft/semantic-kernel-java</url>
                </repository>
            </distributionManagement>
            <repositories>
                <repository>
                    <id>central</id>
                    <url>https://repo1.maven.org/maven2</url>
                </repository>
                <repository>
                    <id>github</id>
                    <name>GitHub Packages</name>
                    <url>https://maven.pkg.github.com/microsoft/semantic-kernel-java</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
        </profile>
    </profiles>

    <developers>
        <developer>
            <id>microsoft</id>
            <name>Microsoft</name>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>MIT License</name>
            <url>https://opensource.org/licenses/MIT</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <url>https://github.com/microsoft/semantic-kernel</url>
        <connection>scm:git:https://github.com/microsoft/semantic-kernel.git</connection>
        <developerConnection>scm:git:https://github.com/microsoft/semantic-kernel.git</developerConnection>
        <tag>HEAD</tag>
    </scm>
</project>
