<?xml version="1.0" encoding="UTF-8" ?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.microsoft.semantic-kernel</groupId>
        <artifactId>semantickernel-samples-parent</artifactId>
        <version>1.4.4-RC2-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>semantickernel-demos</artifactId>
    <packaging>pom</packaging>
    <name>semantic-kernel-demos</name>

    <modules>
        <module>booking-agent-m365</module>
        <module>semantickernel-spring-starter</module>
        <module>sk-presidio-sample</module>
    </modules>
</project>
