<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info">

    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{HH:mm:ss} %-5level %c{1.} - %msg%n"/>
        </Console>
    </Appenders>
    <Loggers>
        <Logger name="reactor.netty" level="warn">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="io.netty" level="warn">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.azure.core.implementation" level="warn">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.microsoft.semantickernel.orchestration.FunctionInvocation" level="info">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.github.victools.jsonschema" level="info">
            <AppenderRef ref="Console"/>
        </Logger>
        <Root level="debug">
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
</Configuration>