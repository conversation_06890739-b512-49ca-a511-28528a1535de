{"schema": 1, "type": "completion", "description": "Creates a chat response to the user", "execution_settings": {"default": {"max_tokens": 1000, "temperature": 0}, "gpt-3.5-turbo": {"model_id": "gpt-3.5-turbo-0613", "max_tokens": 4000, "temperature": 0.1}, "gpt-4": {"model_id": "gpt-4-1106-preview", "max_tokens": 8000, "temperature": 0.3}}, "input_variables": [{"name": "request", "description": "The user's request.", "required": true}, {"name": "history", "description": "The history of the conversation.", "required": true}]}