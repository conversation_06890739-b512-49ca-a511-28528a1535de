name: getIntent
description: Gets the intent of the user.
template: |
  <message role="system">Instructions: What is the intent of this request?
  Do not explain the reasoning, just reply back with the intent. If you are unsure, reply with {{choices.[0]}}.
  Choices: {{choices}}.</message>

  {{#each fewShotExamples}}
      {{#each this}}
          <message role="{{role}}">{{content}}</message>
      {{/each}}
  {{/each}}

  <message role="assistant">Summary of conversation so far:
  {{ConversationSummaryPlugin-SummarizeConversation history}}
  </message>

  <message role="user">{{request}}</message>
  <message role="system">Intent:</message>
template_format: handlebars
input_variables:
  - name:          choices
    description:   The choices for the AI to choose from
    default:       ContinueConversation, EndConversation
  - name:          fewShotExamples
    description:   Few shot examples for the AI to learn from
    is_required:   true
  - name:          request
    description:   The user's request
    is_required:   true
execution_settings:
  default:
    max_tokens:   10
    temperature:  0
  gpt-3.5-turbo:
    model_id:     gpt-35-turbo-2
    max_tokens:   10
    temperature:  0.2
  gpt-4:
    model_id: gpt-4-1106-preview
    max_tokens: 10
    temperature: 0.2
