// Copyright (c) Microsoft. All rights reserved.
package com.microsoft.semantickernel.services.chatcompletion;

/**
 * Role of the author of a chat message
 */
public enum AuthorRole {

    /**
     * A system message helps set the behavior of the assistant.
     */
    SYSTEM("system"),
    /**
     * An assistant message is a message generated by the assistant.
     */
    ASSISTANT("assistant"),
    /**
     * A user message is a message generated by the user.
     */
    USER("user"),

    /**
     * A tool message is a message generated by a tool.
     */
    TOOL("tool");

    private final String role;

    private AuthorRole(String role) {
        this.role = role;
    }

    @Override
    public String toString() {
        return role;
    }
}
