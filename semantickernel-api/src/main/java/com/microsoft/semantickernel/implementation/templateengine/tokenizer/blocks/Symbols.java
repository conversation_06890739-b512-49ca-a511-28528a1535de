// Copyright (c) Microsoft. All rights reserved.
package com.microsoft.semantickernel.implementation.templateengine.tokenizer.blocks;

public class Symbols {

    public static final char BlockStarter = '{';
    public static final char BlockEnder = '}';

    public static final char VarPrefix = '$';
    public static final char NamedArgBlockSeparator = '=';

    public static final char DblQuote = '"';
    public static final char SglQuote = '\'';
    public static final char EscapeChar = '\\';

    public static final char Space = ' ';
    public static final char Tab = '\t';
    public static final char NewLine = '\n';
    public static final char CarriageReturn = '\r';
}
