// Copyright (c) Microsoft. All rights reserved.
package com.microsoft.semantickernel.orchestration;

/**
 * Represents the mode in which a function invocation should return its results.
 */
public enum InvocationReturnMode {
    /**
     * Function invocations that build upon a history of previous invocations, such as Chat
     * Completions, will return the full history of messages.
     */
    FULL_HISTORY,
    /**
     * Function invocations that build upon a history of previous invocations, such as Chat
     * Completions, will return only new messages generated by the given invocation.
     * <p>
     * This is the expected default behavior for most use cases.
     */
    NEW_MESSAGES_ONLY,
    /**
     * Function invocations that build upon a history of previous invocations, such as Cha<PERSON>
     * Completions, will return only the last message generated by the given invocation.
     */
    LAST_MESSAGE_ONLY
}
