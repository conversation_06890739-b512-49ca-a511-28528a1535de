a.named.argument.must.have.a.name=A named argument must have a name
a.named.argument.must.have.a.value=A named argument must have a value
a.value.must.be.defined.using.either.single.quotes.or.double.quotes.not.both=A value must be defined using either single quotes or double quotes, not both
a.value.must.have.single.quotes.or.double.quotes.on.both.sides=A value must have single quotes or double quotes on both sides
a.variable.must.start.with.the.symbol=A variable must start with the symbol {}
a.variable.must.start.with.the.symbol.and.have.a.name=A variable must start with the symbol {} and have a name
access.is.denied=Access is denied
annotation.on.method.is.requesting.a.string=Annotation on method: {} is requesting a String which is not assignable to method type {}, possibly as the type argument has not been provided on the annotation.
attempting.to.modify.function.after.it.has.already.been.subscribed=Attempting to modify function {}.{} after it has already been subscribed to. This is not necessarily an error but may be an unusual pattern and indicate a potential bug.
could.not.find.any.valid.configuration.settings=Could not find any valid configuration settings
could.not.find.configuration.file=Could not find configuration file
could.not.find.value.for.configuration.key=Could not find value for configuration key
could.not.parse.or.load.configuration.file=Could not parse or load configuration file
error.building.generative.model=Error building generative model.
error.generating.chat.completion=Error generating chat completion
error.parsing.prompt=Error parsing prompt
failed.to.load.file.0=Failed to load file: {0}
failed.to.parse.config.file=Failed to parse config file {}
failed.to.read.file=Failed to read file
failed.to.read.file1=Failed to read file {}
for.the.function.0.1.the.unknown.parameter.name.was.detected=For the function {0}.{1}, the unknown parameter name was detected as "{2}" this is argument number {3} to the function, this indicates that the argument name for this function was removed during compilation and semantic-kernel is unable to determine the name of the parameter. To support this function the argument must be annotated with @SKFunctionParameters or @SKFunctionInputAttribute. Alternatively the function was invoked with a required context variable missing and no default value.
function.has.already.been.subscribed.to.this.is.not.necessarily.an.error.but.may.be.an.unusual.pattern=Function {}.{} has already been subscribed to. This is not necessarily an error but may be an unusual pattern.
functions.only.support.named.arguments.after.the.first.argument=Functions only support named arguments after the first argument. Argument {} is not named.
invalid.block.0=Invalid block{0}
no.config.for.in=No config for {} in {}
no.converter.found.for.to=No converter found for {} to {}
no.functions.found.in.class.this.can.be.caused.by=No functions found in class {}. This can be caused by DI frameworks that create proxies, or modules that are not making your methods visible. Try using: KernelPluginFactory.createFromObject(Class<?> clazz, Object target, String pluginName).
no.response=No response
no.service.found.meeting.requirements=No service found meeting requirements
no.variable.type.explicitly.specified.by.calling.withresulttype.for.function=No variable type explicitly specified by calling 'withResultType' for function invocation: {}.{}. This may cause a runtime error (probably a ClassCastException) if the result type is not compatible with the expected type.
plugin.already.exists.overwriting.existing.plugin=Plugin {} already exists, overwriting existing plugin
rendered.prompt=RENDERED PROMPT: \n{}
requested.a.non.existent.service.type.of.consider.requesting.a.textaiservice.instead=Requested a non-existent service type of {}. Consider requesting a TextAIService instead.
something.went.wrong.while.rendering.the.semantic.function.or.while.executing.the.text.completion.function.error=Something went wrong while rendering the semantic function or while executing the text completion. Function: {}.{}. Error: {}
syntax.error.the.template.syntax.used.is.not.valid=Syntax error, the template syntax used is not valid
the.block.type.produced.be.the.tokenizer.was.not.expected=The block type produced be the tokenizer was not expected
the.content.of.the.response.was.invalid=The content of the response was invalid
the.first.arg.of.a.function.must.be.a.quoted.string.variable.or.named.argument=The first arg of a function must be a quoted string, variable or named argument
the.function.is.not.supported=The function is not supported
the.request.timed.out=The request timed out
the.request.was.invalid=The request was invalid
the.request.was.throttled=The request was throttled
the.requested.model.is.not.available=The requested model is not available
the.supplied.configuration.was.invalid=The supplied configuration was invalid
the.template.execution.failed.e.g.a.function.call.threw.an.exception=The template execution failed, e.g. a function call threw an exception
the.template.requires.an.unknown.function=The template requires an unknown function
the.variable.name.contains.invalid.characters.only.alphanumeric.chars.and.underscore.are.allowed=The variable name '{}' contains invalid characters. Only alphanumeric chars and underscore are allowed.
the.variable.name.is.empty=The variable name is empty
there.was.an.error.in.the.service=There was an error in the service
there.was.an.issue.with.the.named.argument.value.for=There was an issue with the named argument value for {}
this.error.indicates.that.you.have.attempted.to.use.a.chat.completion.model=This error indicates that you have attempted to use a chat completion model in a text completion service. Try using a chat completion service instead when building your kernel, for instance when building your service use SKBuilders.chatCompletion() rather than SKBuilders.textCompletionService().
unable.to.load.prompt.template.config.for.in=Unable to load prompt template config for {} in {}
unable.to.load.service.s=Unable to load service %s
unexpected.named.argument.found.expected.function.name.first=Unexpected named argument found. Expected function name first.
unexpected.second.token.found.0=Unexpected second token found: {0}
unknown.error=Unknown error
variable.not.found=Variable `{}{}` not found