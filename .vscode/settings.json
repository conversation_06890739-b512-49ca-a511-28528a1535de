{
  "editor.formatOnType": true,
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": "active",
  "notebook.output.textLineLimit": 500,
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true
  },
  "[java]": {
    "editor.formatOnSave": false,
    "editor.tabSize": 4,
    "editor.codeActionsOnSave": {
      "source.fixAll": "never"
    },
  },
  "java.debug.settings.onBuildFailureProceed": true,
  "java.compile.nullAnalysis.mode": "disabled"
}
