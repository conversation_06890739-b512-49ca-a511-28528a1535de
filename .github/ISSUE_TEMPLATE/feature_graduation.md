---
name: Feature graduation
about: Plan the graduation of an experimental feature
title: 'Graduate XXX feature'
labels: ["feature_graduation"]
projects: ["semantic-kernel"]
assignees: ''

---

---
name: Feature graduation
about: Plan the graduation of an experimental feature

---

Checklist to be completed when graduating an experimental feature

- [ ] Notify PM's and EM's that feature is read for graduation
- [ ] Contact PM for list of sample use cases
- [ ] Verify there are sample implementations​ for each of the use cases
- [ ] Verify telemetry and logging are complete
- [ ] ​Verify API docs are complete and arrange to have them published
- [ ] Make appropriate updates to Learn docs​
- [ ] Make appropriate updates to Concept samples
- [ ] Male appropriate updates to Blog posts
- [ ] Verify there are no serious open Issues​​
- [ ] Update table in EXPERIMENTS.md
- [ ] Remove SKEXP​ flag from the experimental code
