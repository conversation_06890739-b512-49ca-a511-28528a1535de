# Check pull requests for typos.
#
# Configuration: .github/_typos.toml
#
# Info:          https://github.com/marketplace/actions/typos-action
# Local install: brew install typos-cli
# Local install: conda install typos
# Local run:     typos -c .github/_typos.toml

name: Spell Check

on:
  workflow_dispatch:
  pull_request:
    branches: [ "main" ]

jobs:
  run:
    name: Spell Check with Typos
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Use custom config file
        uses: crate-ci/typos@master
        with:
          config: .github/_typos.toml
          write_changes: false
