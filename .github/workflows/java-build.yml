name: Build Java Semantic Kernel

# Triggers the workflow on manual dispatch, push, and pull request events
# for the specified branches and paths
on:
  workflow_dispatch:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

permissions:
  contents: read

jobs:
  # Builds and tests the Java project
  java-build:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        # Defines a matrix strategy for JDK versions 8 and 17
        java-versions: [8, 17]

    name: Java CI on JDK${{ matrix.java-versions }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      # Need to use JDK 11 to build for JDK 8
      - name: Set JDK
        id: set-jdk
        shell: bash
        run: |
          if [[ ${{ matrix.java-versions }} == 8 ]]; then
            echo "JDK_VERSION=11" >> $GITHUB_OUTPUT
          else
            version=${{ matrix.java-versions }}
            echo "JDK_VERSION=$version" >> $GITHUB_OUTPUT
          fi

      # Sets up the specified JDK version from the matrix
      - uses: actions/setup-java@v4
        with:
          java-version: ${{ steps.set-jdk.outputs.JDK_VERSION }}
          distribution: microsoft
          cache: maven

      # Builds the project with <PERSON><PERSON> using the matrix JDK version
      - name: Build with <PERSON><PERSON>
        run: ./mvnw -B -Pbug-check -DskipTests -Pcompile-jdk${{ matrix.java-versions }} clean install --file pom.xml

      # Runs tests with Maven using the matrix JDK version
      - name: Run tests
        run: ./mvnw -B -Pbug-check -Pcompile-jdk${{ matrix.java-versions }} test --file pom.xml

      # Uploads test artifacts for each JDK version
      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test_output_sk_jdk${{ matrix.java-versions }}u
          path: ./**/target/surefire-reports/*Test.txt
