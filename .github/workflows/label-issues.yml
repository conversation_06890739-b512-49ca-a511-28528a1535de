name: Label issues
on:
  issues:
    types:
      - reopened
      - opened

jobs:
  label_issues:
    name: "Issue: add labels"
    if: ${{ github.event.action == 'opened' || github.event.action == 'reopened' }}
    runs-on: ubuntu-latest
    permissions:
      issues: write
    steps:
      - uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GH_ACTIONS_PR_WRITE }}
          script: |
            // Get the issue body and title
            const body = context.payload.issue.body
            let title = context.payload.issue.title
            
            // Define the labels array
            let labels = ["triage"]
            
            // Check if the body or the title contains the word 'python' (case-insensitive)
            if ((body != null && body.match(/python/i)) || (title != null && title.match(/python/i))) {
              // Add the 'python' label to the array
              labels.push("python")
            }
            
            // Check if the body or the title contains the word 'java' (case-insensitive)
            if ((body != null && body.match(/java/i)) || (title != null && title.match(/java/i))) {
              // Add the 'java' label to the array
              labels.push("java")
            }
            
            // Check if the body or the title contains the words 'dotnet', '.net', 'c#' or 'csharp' (case-insensitive)
            if ((body != null && body.match(/.net/i)) || (title != null && title.match(/.net/i)) ||
                (body != null && body.match(/dotnet/i)) || (title != null && title.match(/dotnet/i)) ||
                (body != null && body.match(/C#/i)) || (title != null && title.match(/C#/i)) ||
                (body != null && body.match(/csharp/i)) || (title != null && title.match(/csharp/i))) {
              // Add the '.NET' label to the array
              labels.push(".NET")
            }

            // Add the labels to the issue
            github.rest.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: labels
            });
