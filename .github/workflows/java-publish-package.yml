name: Deploy Java Semantic Kernel Package

# Triggers the workflow on merging a PR
on:
  pull_request:
    types:
      - closed
    branches: [ "main" ]

permissions:
  contents: read
  packages: write

jobs:
  if_merged:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      # Sets up the specified JDK version from the matrix
      - uses: actions/setup-java@v4
        with:
          java-version: 11
          distribution: microsoft
          cache: maven

      - name: Build artifacts
        run: ./mvnw -B -DskipTests -Pcompile-jdk8 -P-compile-jdk17 clean deploy --file pom.xml -DaltDeploymentRepository=local::file:///tmp/target/staging-deploy

      - name: Upload Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: Artifacts
          path: /tmp/target/staging-deploy

      - name: Deploy to github packages
        run: ./mvnw -B -DskipTests -Pcompile-jdk8 -P-compile-jdk17 -Pgithub-packages clean deploy --file pom.xml
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
