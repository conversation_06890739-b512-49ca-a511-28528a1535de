name: Check .md links

on:
  workflow_dispatch:
  pull_request:
    branches: [ "main" ]

permissions:
  contents: read

jobs:
  markdown-link-check:
    runs-on: ubuntu-latest
    # check out the latest version of the code
    steps:
      - uses: actions/checkout@v4

      # Checks the status of hyperlinks in .md files in verbose mode
      - name: Check links
        uses: gaurav-nelson/github-action-markdown-link-check@v1
        with:
          use-verbose-mode: "yes"
          config-file: ".github/workflows/markdown-link-check-config.json"
