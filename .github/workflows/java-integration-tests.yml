name: Run Java Integration Tests and Samples

on:
  workflow_dispatch:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

permissions:
  contents: read

jobs:
  java-integration-tests:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        java-versions: [8, 17]

    name: Java Tests on JDK${{ matrix.java-versions }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      # Need to use JDK 11 to build for JDK 8
      - name: Set JDK
        id: set-jdk
        shell: bash
        run: |
          if [[ ${{ matrix.java-versions }} == 8 ]]; then
            echo "JDK_VERSION=11" >> $GITHUB_OUTPUT
          else
            version=${{ matrix.java-versions }}
            echo "JDK_VERSION=$version" >> $GITHUB_OUTPUT
          fi

      - uses: actions/setup-java@v4
        with:
          java-version: ${{ steps.set-jdk.outputs.JDK_VERSION }}
          distribution: microsoft
          cache: maven

      - name: Build with Maven
        run: ./mvnw -B -Pwith-samples -Pbug-check -DskipTests -Pcompile-jdk${{ matrix.java-versions }} clean install --file pom.xml
        if: ${{ matrix.java-versions >= 17 }}

      # API tests run on JDK 17+
      - name: Run integration tests
        run: ../mvnw -B -Pbug-check clean install --file pom.xml
        working-directory: api-test
        env:
          OPENAI_API_KEY: ""
          AZURE_OPENAI_ENDPOINT: ""
          AZURE_OPENAI_API_KEY: ""
          AZURE_OPENAI_DEPLOYMENT_NAME: ""
        if: ${{ matrix.java-versions >= 17 }}

      # Samples build on JDK 17+
      - name: Build semantic-kernel samples
        run: ../mvnw -B clean install --file pom.xml
        working-directory: samples
        if: ${{ matrix.java-versions >= 17 }}
