# Typos configuration file
#
# Info:    https://github.com/marketplace/actions/typos-action
# Install: brew install typos-cli
# Install: conda install typos
# Run:     typos -c .github/_typos.toml

[files]
extend-exclude = [
    "_typos.toml",
    "package-lock.json",
    "*.bicep",
    "encoder.json",
    "vocab.bpe",
    "CodeTokenizerTests.cs",
    "test_code_tokenizer.py",
    "*response.json",
    "samples/semantickernel-demos/sk-presidio-sample/README.md"
]

[default.extend-words]
ACI = "ACI"               # Azure Container Instance
exercize = "exercize"     # test typos
gramatical = "gramatical" # test typos
Guid = "Guid"             # Globally Unique Identifier
HD = "HD"                 # Test header value
EOF = "EOF"               # End of File
ans = "ans"               # Short for answers
arange = "arange"         # Method in Python numpy package
prompty = "prompty"       # prompty is a format name.
ist = "ist"               # German language
Prelease = "Prelease"     # Prelease is a format name.

[default.extend-identifiers]
ags = "ags" # Azure Graph Service

[type.jupyter]
extend-ignore-re = [
    '"[A-Fa-f0-9]{8}"', # cell id strings
]

[type.msbuild]
extend-ignore-re = [
    'Version=".*"', # ignore package version numbers
]
