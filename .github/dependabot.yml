# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  # Maintain dependencies for nuget
  - package-ecosystem: "nuget"
    directory: "dotnet/"
    schedule:
      interval: "weekly"
      day: "monday"
    ignore:
      # For all System.* and Microsoft.Extensions/Bcl.* packages, ignore all major version updates
      - dependency-name: "System.*"
        update-types: ["version-update:semver-major"]
      - dependency-name: "Microsoft.Extensions.*"
        update-types: ["version-update:semver-major"]
      - dependency-name: "Microsoft.Bcl.*"
        update-types: ["version-update:semver-major"]
      - dependency-name: "Moq"
    labels:
      - ".NET"
      - "dependencies"

  # Maintain dependencies for nuget
  - package-ecosystem: "nuget"
    directory: "samples/dotnet"
    schedule:
      interval: "weekly"
      day: "monday"

  # Maintain dependencies for npm
  - package-ecosystem: "npm"
    directory: "samples/apps"
    schedule:
      interval: "weekly"
      day: "monday"

  # Maintain dependencies for pip
  - package-ecosystem: "pip"
    directory: "python/"
    schedule:
      interval: "weekly"
      day: "monday"
    labels:
      - "python"
      - "dependencies"

  # Maintain dependencies for github-actions
  - package-ecosystem: "github-actions"
    # Workflow files stored in the
    # default location of `.github/workflows`
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
