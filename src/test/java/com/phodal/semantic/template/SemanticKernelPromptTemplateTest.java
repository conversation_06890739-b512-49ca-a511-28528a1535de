package com.phodal.semantic.template;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

public class SemanticKernelPromptTemplateTest {

    @Test
    void testBasicVariableReplacement() {
        String templateStr = "Hello {{$name}}, welcome to {{$place}}!";
        SemanticKernelPromptTemplate template = new SemanticKernelPromptTemplate(templateStr);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("name", "Alice");
        variables.put("place", "Wonderland");
        
        String result = template.render(variables);
        assertEquals("Hello Alice, welcome to Wonderland!", result);
    }

    @Test
    void testVariableWithSpaces() {
        String templateStr = "Hello {{ $name }}, your score is {{ $score }}.";
        SemanticKernelPromptTemplate template = new SemanticKernelPromptTemplate(templateStr);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("name", "Bob");
        variables.put("score", 95);
        
        String result = template.render(variables);
        assertEquals("Hello Bob, your score is 95.", result);
    }

    @Test
    void testSpecialVariableReplacement() {
        String templateStr = "Available functions: {{ActionPlanner_Excluded.ListOfFunctions}}";
        SemanticKernelPromptTemplate template = new SemanticKernelPromptTemplate(templateStr);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("ActionPlanner_Excluded.ListOfFunctions", "function1\nfunction2\nfunction3");
        
        String result = template.render(variables);
        assertEquals("Available functions: function1\nfunction2\nfunction3", result);
    }

    @Test
    void testMissingVariables() {
        String templateStr = "Hello {{$name}}, your age is {{$age}}.";
        SemanticKernelPromptTemplate template = new SemanticKernelPromptTemplate(templateStr);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("name", "Charlie");
        // age is missing
        
        String result = template.render(variables);
        assertEquals("Hello Charlie, your age is .", result);
    }

    @Test
    void testGetVariableNames() {
        String templateStr = "Hello {{$name}}, welcome to {{$place}}! Functions: {{ActionPlanner_Excluded.ListOfFunctions}}";
        SemanticKernelPromptTemplate template = new SemanticKernelPromptTemplate(templateStr);
        
        Set<String> variables = template.getVariableNames();
        assertEquals(3, variables.size());
        assertTrue(variables.contains("name"));
        assertTrue(variables.contains("place"));
        assertTrue(variables.contains("ActionPlanner_Excluded.ListOfFunctions"));
    }

    @Test
    void testTemplateValidation() {
        // Valid template
        SemanticKernelPromptTemplate validTemplate = new SemanticKernelPromptTemplate("Hello {{$name}}!");
        assertTrue(validTemplate.isValid());
        
        // Invalid template - unmatched braces
        SemanticKernelPromptTemplate invalidTemplate = new SemanticKernelPromptTemplate("Hello {{$name}!");
        assertFalse(invalidTemplate.isValid());
        
        // Another invalid template
        SemanticKernelPromptTemplate invalidTemplate2 = new SemanticKernelPromptTemplate("Hello {$name}}!");
        assertFalse(invalidTemplate2.isValid());
    }

    @Test
    void testEmptyTemplate() {
        SemanticKernelPromptTemplate template = new SemanticKernelPromptTemplate("");
        
        Map<String, Object> variables = new HashMap<>();
        String result = template.render(variables);
        assertEquals("", result);
        
        assertTrue(template.getVariableNames().isEmpty());
        assertTrue(template.isValid());
    }

    @Test
    void testNullTemplate() {
        SemanticKernelPromptTemplate template = new SemanticKernelPromptTemplate(null);
        
        Map<String, Object> variables = new HashMap<>();
        String result = template.render(variables);
        assertEquals("", result);
        
        assertTrue(template.getVariableNames().isEmpty());
        assertFalse(template.isValid());
    }

    @Test
    void testComplexTemplate() {
        String templateStr = """
            你是一个智能助手，请根据用户的需求选择合适的工具。
            
            可用工具：
            {{ActionPlanner_Excluded.ListOfFunctions}}
            
            用户输入：{{$input}}
            
            请选择最合适的工具来处理用户请求。
            """;
        
        SemanticKernelPromptTemplate template = new SemanticKernelPromptTemplate(templateStr);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("input", "请帮我查询天气");
        variables.put("ActionPlanner_Excluded.ListOfFunctions", "weather_tool\ntime_tool\ncalculator_tool");
        
        String result = template.render(variables);
        
        assertTrue(result.contains("请帮我查询天气"));
        assertTrue(result.contains("weather_tool"));
        assertTrue(result.contains("time_tool"));
        assertTrue(result.contains("calculator_tool"));
    }

    @Test
    void testCreateFactoryMethod() {
        String templateStr = "Hello {{$name}}!";
        SemanticKernelPromptTemplate template = SemanticKernelPromptTemplate.create(templateStr);
        
        assertNotNull(template);
        assertEquals(templateStr, template.getTemplate());
        assertEquals(1, template.getVariableNames().size());
        assertTrue(template.getVariableNames().contains("name"));
    }
}
