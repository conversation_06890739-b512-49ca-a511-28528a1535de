package com.phodal.semantic.template;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * 模板系统演示
 * 展示如何使用新的模板系统来处理 skprompt.txt 中的变量替换
 */
public class TemplateSystemDemo {

    @Test
    void demonstrateOriginalTemplate() {
        // 模拟原始的 skprompt.txt 模板内容
        String originalTemplate = """
            你是一个智能交易助手，会根据用户最新的需求，每次选择最合适的1个函数，操作订单或回复用户问题。
            从函数列表开始到函数列表结束包含了所有你可以使用的函数，你不能调用这些函数以外的函数。
            请对用户最新的文本进行回复。
            
            [实际场景开始]
            - 函数列表开始:
            {{ActionPlanner_Excluded.ListOfFunctions}}
            - 函数列表结束.
            [交互内容]
            {{ $input }}
            [返回体]
            """;

        // 创建模板实例
        SemanticKernelPromptTemplate template = SemanticKernelPromptTemplate.create(originalTemplate);

        // 准备变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("input", "外代销产品，请问给代销机构下发数据一般是几点？");
        
        // 模拟工具列表
        String toolsList = """
            // 交互内容中存在和问题相关的信息时，请调用这个函数，根据交互内容或给出的文本，回答用户的问题
            general_plugins.context_qa
            请求参数 "input": 用户输入文本.
            
            // 根据订单信息查询订单价格
            order_plugin.inquiry_order_price
            请求参数 "input": 用户输入文本.
            
            // 根据订单信息执行开仓、下单、行权操作
            order_plugin.place_order
            请求参数 "input": 用户输入文本.
            """;
        
        variables.put("ActionPlanner_Excluded.ListOfFunctions", toolsList);

        // 渲染模板
        String rendered = template.render(variables);

        // 输出结果
        System.out.println("=== 原始模板渲染结果 ===");
        System.out.println(rendered);
        System.out.println();

        // 验证变量提取
        System.out.println("=== 模板中的变量 ===");
        template.getVariableNames().forEach(var -> System.out.println("- " + var));
        System.out.println();

        // 验证模板有效性
        System.out.println("=== 模板验证 ===");
        System.out.println("模板是否有效: " + template.isValid());
    }

    @Test
    void demonstrateCustomTemplate() {
        // 演示自定义模板
        String customTemplate = """
            你是一个专业的{{$domain}}助手。
            
            当前时间: {{$currentTime}}
            用户级别: {{$userLevel}}
            
            用户问题: {{$input}}
            
            可用工具:
            {{ActionPlanner_Excluded.ListOfFunctions}}
            
            请根据用户的{{$domain}}需求，选择合适的工具来处理问题。
            """;

        SemanticKernelPromptTemplate template = SemanticKernelPromptTemplate.create(customTemplate);

        Map<String, Object> variables = new HashMap<>();
        variables.put("domain", "金融投资");
        variables.put("currentTime", "2025-08-14 16:30:00");
        variables.put("userLevel", "VIP");
        variables.put("input", "我想了解一下期权交易的风险");
        variables.put("ActionPlanner_Excluded.ListOfFunctions", 
            "// 风险评估工具\nrisk_assessment.evaluate\n请求参数 \"input\": 用户输入文本.\n\n" +
            "// 期权知识问答\noption_qa.answer\n请求参数 \"input\": 用户输入文本.");

        String rendered = template.render(variables);

        System.out.println("=== 自定义模板渲染结果 ===");
        System.out.println(rendered);
        System.out.println();

        System.out.println("=== 自定义模板变量 ===");
        template.getVariableNames().forEach(var -> System.out.println("- " + var));
    }

    @Test
    void demonstrateTemplateValidation() {
        System.out.println("=== 模板验证演示 ===");

        // 有效模板
        String validTemplate = "Hello {{$name}}, welcome to {{$place}}!";
        SemanticKernelPromptTemplate valid = SemanticKernelPromptTemplate.create(validTemplate);
        System.out.println("有效模板: " + valid.isValid() + " - " + validTemplate);

        // 无效模板 - 不匹配的大括号
        String invalidTemplate1 = "Hello {{$name}, welcome to {{$place}}!";
        SemanticKernelPromptTemplate invalid1 = SemanticKernelPromptTemplate.create(invalidTemplate1);
        System.out.println("无效模板1: " + invalid1.isValid() + " - " + invalidTemplate1);

        // 无效模板 - 多余的大括号
        String invalidTemplate2 = "Hello {{$name}}, welcome to {{$place}}}!";
        SemanticKernelPromptTemplate invalid2 = SemanticKernelPromptTemplate.create(invalidTemplate2);
        System.out.println("无效模板2: " + invalid2.isValid() + " - " + invalidTemplate2);

        // 空模板
        SemanticKernelPromptTemplate empty = SemanticKernelPromptTemplate.create("");
        System.out.println("空模板: " + empty.isValid() + " - (空字符串)");

        // null模板
        SemanticKernelPromptTemplate nullTemplate = SemanticKernelPromptTemplate.create(null);
        System.out.println("null模板: " + nullTemplate.isValid() + " - null");
    }

    @Test
    void demonstrateVariableExtraction() {
        System.out.println("=== 变量提取演示 ===");

        String complexTemplate = """
            系统: {{$systemRole}}
            用户: {{$userName}} (级别: {{$userLevel}})
            时间: {{$timestamp}}
            
            问题: {{$input}}
            
            工具列表:
            {{ActionPlanner_Excluded.ListOfFunctions}}
            
            配置:
            {{Config.MaxTokens}}
            {{Config.Temperature}}
            """;

        SemanticKernelPromptTemplate template = SemanticKernelPromptTemplate.create(complexTemplate);

        System.out.println("模板中发现的变量:");
        template.getVariableNames().forEach(var -> {
            if (var.contains(".")) {
                System.out.println("- " + var + " (特殊变量)");
            } else {
                System.out.println("- " + var + " (普通变量)");
            }
        });

        System.out.println("\n变量总数: " + template.getVariableNames().size());
    }

    @Test
    void demonstrateErrorHandling() {
        System.out.println("=== 错误处理演示 ===");

        String template = "Hello {{$name}}, your score is {{$score}}, location: {{$location}}";
        SemanticKernelPromptTemplate promptTemplate = SemanticKernelPromptTemplate.create(template);

        // 部分变量缺失
        Map<String, Object> partialVariables = new HashMap<>();
        partialVariables.put("name", "Alice");
        // score 和 location 缺失

        String result = promptTemplate.render(partialVariables);
        System.out.println("部分变量缺失的结果: " + result);

        // 变量类型不匹配（但会自动转换为字符串）
        Map<String, Object> mixedVariables = new HashMap<>();
        mixedVariables.put("name", "Bob");
        mixedVariables.put("score", 95.5); // 数字
        mixedVariables.put("location", new String[]{"Beijing", "Shanghai"}); // 数组

        String result2 = promptTemplate.render(mixedVariables);
        System.out.println("混合类型变量的结果: " + result2);

        // 空变量映射
        String result3 = promptTemplate.render(new HashMap<>());
        System.out.println("空变量映射的结果: " + result3);

        // null变量映射
        String result4 = promptTemplate.render(null);
        System.out.println("null变量映射的结果: " + result4);
    }
}
