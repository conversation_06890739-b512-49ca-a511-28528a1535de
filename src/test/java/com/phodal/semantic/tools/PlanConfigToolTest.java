package com.phodal.semantic.tools;

import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallback;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 测试 plan_config 配置生成工具
 */
class PlanConfigToolTest {

    @Test
    void testPlanConfigToolGeneration() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);
        
        // 创建配置工具生成器
        ConfigBasedToolGenerator configBasedToolGenerator = new ConfigBasedToolGenerator(mockChatClient);
        
        // 测试生成 plan_config 工具
        List<ToolCallback> toolCallbacks = configBasedToolGenerator.generateToolCallbacks("plan_config");
        
        assertNotNull(toolCallbacks);
        assertEquals(1, toolCallbacks.size());
        
        ToolCallback planConfigTool = toolCallbacks.get(0);
        
        // 验证工具定义
        assertEquals("plan_config", planConfigTool.getToolDefinition().name());
        assertEquals("当用户询问关于业务流程、时间安排、操作规范等规划相关问题时，请调用这个函数来提供专业的规划建议", 
                     planConfigTool.getToolDefinition().description());
        
        // 验证输入 Schema
        String inputSchema = planConfigTool.getToolDefinition().inputSchema();
        assertNotNull(inputSchema);
        assertTrue(inputSchema.contains("context"));
        assertTrue(inputSchema.contains("question"));
        assertTrue(inputSchema.contains("相关的业务背景信息或历史对话内容"));
        assertTrue(inputSchema.contains("用户的具体规划问题或需求"));
        
        System.out.println("=== Plan Config 工具测试 ===");
        System.out.println("工具名称: " + planConfigTool.getToolDefinition().name());
        System.out.println("工具描述: " + planConfigTool.getToolDefinition().description());
        System.out.println("输入 Schema: " + inputSchema);
        System.out.println("=== 测试完成 ===");
    }

    @Test
    void testPlanBusinessWithPlanConfigTool() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);
        
        // 创建配置工具生成器
        ConfigBasedToolGenerator configBasedToolGenerator = new ConfigBasedToolGenerator(mockChatClient);
        
        // 创建工具加载器
        ToolLoader toolLoader = new ToolLoader(configBasedToolGenerator);
        
        // 测试加载 plan_config 业务的工具
        List<ToolCallback> tools = toolLoader.getAllAvailableTools("plan_config");
        
        // 验证结果
        assertNotNull(tools);
        assertFalse(tools.isEmpty());
        
        // 检查是否包含 plan_config 工具
        boolean hasPlanConfigTool = tools.stream()
                .anyMatch(tool -> "plan_config".equals(tool.getToolDefinition().name()));
        
        // 检查是否包含通用工具（因为 plan_config 是纯配置生成的业务，不会有传统工具）
        boolean hasGeneralToolsOnly = tools.stream()
                .allMatch(tool -> tool.getToolDefinition().description().contains("总结") ||
                                tool.getToolDefinition().description().contains("翻译") ||
                                tool.getToolDefinition().description().contains("邮件") ||
                                tool.getToolDefinition().description().contains("诗") ||
                                tool.getToolDefinition().description().contains("想法") ||
                                tool.getToolDefinition().description().contains("问答") ||
                                tool.getToolDefinition().description().contains("聊天") ||
                                tool.getToolDefinition().description().contains("时间") ||
                                "plan_config".equals(tool.getToolDefinition().name()));
        
        // 检查是否包含通用工具
        boolean hasGeneralTools = tools.stream()
                .anyMatch(tool -> tool.getToolDefinition().description().contains("总结") ||
                                tool.getToolDefinition().description().contains("翻译"));
        
        System.out.println("=== Plan Config 业务组合工具测试 ===");
        System.out.println("总工具数量: " + tools.size());
        System.out.println("包含 plan_config 工具: " + hasPlanConfigTool);
        System.out.println("包含通用工具: " + hasGeneralTools);

        // 打印所有工具
        System.out.println("所有工具列表:");
        tools.forEach(tool ->
            System.out.println("  - " + tool.getToolDefinition().name() + ": " +
                             tool.getToolDefinition().description()));

        assertTrue(hasPlanConfigTool, "Should contain plan_config tool");
        assertTrue(hasGeneralTools, "Should contain general tools");
        System.out.println("=== 测试完成 ===");
    }
}
