package com.phodal.semantic.tools;

import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallback;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 快速验证 plan_config 工具
 */
class QuickPlanConfigTest {

    @Test
    void testPlanConfigExists() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);
        
        // 创建配置工具生成器
        ConfigBasedToolGenerator generator = new ConfigBasedToolGenerator(mockChatClient);
        
        // 测试生成 plan_config 工具
        List<ToolCallback> tools = generator.generateToolCallbacks("plan_config");
        
        System.out.println("=== Quick Plan Config Test ===");
        System.out.println("Generated tools count: " + tools.size());
        
        if (!tools.isEmpty()) {
            ToolCallback tool = tools.get(0);
            System.out.println("Tool name: " + tool.getToolDefinition().name());
            System.out.println("Tool description: " + tool.getToolDefinition().description());
            System.out.println("Input schema: " + tool.getToolDefinition().inputSchema());
        }
        
        assertFalse(tools.isEmpty(), "Should generate plan_config tool");
        assertEquals("plan_config", tools.get(0).getToolDefinition().name());
        System.out.println("=== Test Passed ===");
    }

    @Test
    void testPlanConfigInToolLoader() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);
        
        // 创建配置工具生成器和工具加载器
        ConfigBasedToolGenerator generator = new ConfigBasedToolGenerator(mockChatClient);
        ToolLoader toolLoader = new ToolLoader(generator);
        
        // 测试加载 plan_config 业务的工具
        List<ToolCallback> allTools = toolLoader.getAllAvailableTools("plan_config");
        
        System.out.println("=== Plan Config in ToolLoader Test ===");
        System.out.println("Total tools: " + allTools.size());
        
        // 查找 plan_config 工具
        boolean hasPlanConfigTool = allTools.stream()
                .anyMatch(tool -> "plan_config".equals(tool.getToolDefinition().name()));
        
        System.out.println("Has plan_config tool: " + hasPlanConfigTool);
        
        // 打印所有工具名称
        System.out.println("All tools:");
        allTools.forEach(tool -> 
            System.out.println("  - " + tool.getToolDefinition().name()));
        
        assertTrue(hasPlanConfigTool, "Should contain plan_config tool");
        System.out.println("=== Test Passed ===");
    }
}
