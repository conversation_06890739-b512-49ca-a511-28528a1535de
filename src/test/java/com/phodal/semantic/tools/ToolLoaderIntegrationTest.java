package com.phodal.semantic.tools;

import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallback;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ToolLoaderIntegrationTest {

    @Test
    void testLoadContextQATools() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);
        
        // 创建配置工具生成器
        ConfigBasedToolGenerator configBasedToolGenerator = new ConfigBasedToolGenerator(mockChatClient);
        
        // 创建工具加载器
        ToolLoader toolLoader = new ToolLoader(configBasedToolGenerator);
        
        // 测试加载 context_qa 工具
        List<ToolCallback> tools = toolLoader.getAllAvailableTools("context_qa");
        
        // 验证结果
        assertNotNull(tools);
        assertFalse(tools.isEmpty());
        
        // 应该包含通用工具和配置生成的工具
        boolean hasContextQATool = tools.stream()
                .anyMatch(tool -> "context_qa".equals(tool.getToolDefinition().name()));
        
        assertTrue(hasContextQATool, "Should contain context_qa tool");
        
        // 验证工具定义
        ToolCallback contextQATool = tools.stream()
                .filter(tool -> "context_qa".equals(tool.getToolDefinition().name()))
                .findFirst()
                .orElse(null);
        
        assertNotNull(contextQATool);
        assertEquals("交互内容中存在和问题相关的信息时，请调用这个函数，根据交互内容或给出的文本，回答用户的问题", 
                     contextQATool.getToolDefinition().description());
    }

    @Test
    void testLoadTraditionalTools() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);
        
        // 创建配置工具生成器
        ConfigBasedToolGenerator configBasedToolGenerator = new ConfigBasedToolGenerator(mockChatClient);
        
        // 创建工具加载器
        ToolLoader toolLoader = new ToolLoader(configBasedToolGenerator);
        
        // 测试加载传统的 QA 工具
        List<ToolCallback> tools = toolLoader.getAllAvailableTools("qa");
        
        // 验证结果
        assertNotNull(tools);
        assertFalse(tools.isEmpty());
        
        // 应该包含通用工具和 QA 工具
        boolean hasQATools = tools.stream()
                .anyMatch(tool -> tool.getToolDefinition().description().contains("舆情") ||
                                tool.getToolDefinition().description().contains("检索"));

        assertTrue(hasQATools, "Should contain QA tools");
    }

    @Test
    void testFallbackToConfigBasedTools() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);
        
        // 创建配置工具生成器
        ConfigBasedToolGenerator configBasedToolGenerator = new ConfigBasedToolGenerator(mockChatClient);
        
        // 创建工具加载器
        ToolLoader toolLoader = new ToolLoader(configBasedToolGenerator);
        
        // 测试加载未知业务，应该尝试配置生成的工具
        List<ToolCallback> tools = toolLoader.getAllAvailableTools("unknown_business");
        
        // 验证结果 - 应该只包含通用工具，因为没有对应的配置文件
        assertNotNull(tools);
        assertFalse(tools.isEmpty());
        
        // 应该只包含通用工具（没有配置生成的工具）
        boolean hasNoConfigBasedTools = tools.stream()
                .noneMatch(tool -> "unknown_business".equals(tool.getToolDefinition().name()));

        assertTrue(hasNoConfigBasedTools, "Should not contain config-based tools for unknown business");
    }
}
