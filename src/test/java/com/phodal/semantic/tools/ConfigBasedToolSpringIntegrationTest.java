package com.phodal.semantic.tools;

import org.junit.jupiter.api.Test;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Spring 集成测试，验证配置生成工具在实际 Spring 上下文中的工作情况
 */
@SpringBootTest
@TestPropertySource(properties = {
    "spring.ai.openai.api-key=test-key",
    "spring.ai.openai.base-url=http://localhost:8080/v1"
})
class ConfigBasedToolSpringIntegrationTest {

    @Autowired
    private ConfigBasedToolGenerator configBasedToolGenerator;

    @Autowired
    private ToolLoader toolLoader;

    @Test
    void testConfigBasedToolGeneratorBean() {
        // 验证 ConfigBasedToolGenerator Bean 正确注入
        assertNotNull(configBasedToolGenerator, "ConfigBasedToolGenerator should be autowired");
    }

    @Test
    void testToolLoaderBean() {
        // 验证 ToolLoader Bean 正确注入
        assertNotNull(toolLoader, "ToolLoader should be autowired");
    }

    @Test
    void testGenerateContextQAToolInSpringContext() {
        // 在 Spring 上下文中测试生成 context_qa 工具
        List<ToolCallback> toolCallbacks = configBasedToolGenerator.generateToolCallbacks("context_qa");
        
        assertNotNull(toolCallbacks, "Tool callbacks should not be null");
        assertEquals(1, toolCallbacks.size(), "Should generate exactly one tool callback");
        
        ToolCallback contextQATool = toolCallbacks.get(0);
        assertNotNull(contextQATool, "Context QA tool should not be null");
        
        // 验证工具定义
        assertEquals("context_qa", contextQATool.getToolDefinition().name());
        assertEquals("交互内容中存在和问题相关的信息时，请调用这个函数，根据交互内容或给出的文本，回答用户的问题", 
                     contextQATool.getToolDefinition().description());
        
        // 验证输入 Schema
        String inputSchema = contextQATool.getToolDefinition().inputSchema();
        assertNotNull(inputSchema, "Input schema should not be null");
        assertTrue(inputSchema.contains("input"), "Schema should contain 'input' field");
        assertTrue(inputSchema.contains("query"), "Schema should contain 'query' field");
        
        System.out.println("=== Spring 集成测试 - 配置生成工具 ===");
        System.out.println("工具名称: " + contextQATool.getToolDefinition().name());
        System.out.println("工具描述: " + contextQATool.getToolDefinition().description());
        System.out.println("输入 Schema: " + inputSchema);
        System.out.println("=== 测试完成 ===");
    }

    @Test
    void testToolLoaderWithConfigBasedToolsInSpringContext() {
        // 在 Spring 上下文中测试 ToolLoader 加载配置生成的工具
        List<ToolCallback> allTools = toolLoader.getAllAvailableTools("context_qa");
        
        assertNotNull(allTools, "All tools should not be null");
        assertFalse(allTools.isEmpty(), "Should have at least some tools");
        
        // 查找 context_qa 工具
        ToolCallback contextQATool = allTools.stream()
                .filter(tool -> "context_qa".equals(tool.getToolDefinition().name()))
                .findFirst()
                .orElse(null);
        
        assertNotNull(contextQATool, "Should contain context_qa tool");
        assertEquals("交互内容中存在和问题相关的信息时，请调用这个函数，根据交互内容或给出的文本，回答用户的问题", 
                     contextQATool.getToolDefinition().description());
        
        // 验证还包含通用工具
        boolean hasGeneralTools = allTools.stream()
                .anyMatch(tool -> tool.getToolDefinition().description().contains("总结") ||
                                tool.getToolDefinition().description().contains("翻译"));
        
        assertTrue(hasGeneralTools, "Should also contain general tools");
        
        System.out.println("=== Spring 集成测试 - ToolLoader ===");
        System.out.println("总工具数量: " + allTools.size());
        System.out.println("包含 context_qa 工具: " + (contextQATool != null));
        System.out.println("包含通用工具: " + hasGeneralTools);
        
        // 打印所有工具名称
        System.out.println("所有工具:");
        allTools.forEach(tool -> 
            System.out.println("  - " + tool.getToolDefinition().name() + ": " + 
                             tool.getToolDefinition().description()));
        System.out.println("=== 测试完成 ===");
    }

    @Test
    void testConfigBasedToolFallbackInSpringContext() {
        // 测试未知业务的配置生成工具回退机制
        List<ToolCallback> unknownBusinessTools = toolLoader.getAllAvailableTools("unknown_business");
        
        assertNotNull(unknownBusinessTools, "Unknown business tools should not be null");
        assertFalse(unknownBusinessTools.isEmpty(), "Should have at least general tools");
        
        // 应该不包含 unknown_business 工具（因为没有对应的配置文件）
        boolean hasUnknownBusinessTool = unknownBusinessTools.stream()
                .anyMatch(tool -> "unknown_business".equals(tool.getToolDefinition().name()));
        
        assertFalse(hasUnknownBusinessTool, "Should not contain unknown_business tool");
        
        System.out.println("=== Spring 集成测试 - 回退机制 ===");
        System.out.println("未知业务工具数量: " + unknownBusinessTools.size());
        System.out.println("包含未知业务工具: " + hasUnknownBusinessTool);
        System.out.println("=== 测试完成 ===");
    }
}
