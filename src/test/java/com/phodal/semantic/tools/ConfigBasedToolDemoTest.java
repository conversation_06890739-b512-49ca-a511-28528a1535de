package com.phodal.semantic.tools;

import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallback;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 配置生成工具演示测试
 */
class ConfigBasedToolDemoTest {

    @Test
    void demonstrateConfigBasedToolExecution() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);

        // 创建配置工具生成器
        ConfigBasedToolGenerator generator = new ConfigBasedToolGenerator(mockChatClient);

        // 生成 context_qa 工具
        List<ToolCallback> toolCallbacks = generator.generateToolCallbacks("context_qa");

        assertNotNull(toolCallbacks);
        assertEquals(1, toolCallbacks.size());

        ToolCallback contextQATool = toolCallbacks.get(0);

        // 验证工具定义
        assertEquals("context_qa", contextQATool.getToolDefinition().name());
        assertEquals("交互内容中存在和问题相关的信息时，请调用这个函数，根据交互内容或给出的文本，回答用户的问题",
                     contextQATool.getToolDefinition().description());

        // 验证工具元数据
        assertNotNull(contextQATool.getToolMetadata());
        assertFalse(contextQATool.getToolMetadata().returnDirect());

        System.out.println("=== 配置生成工具演示 ===");
        System.out.println("工具名称: " + contextQATool.getToolDefinition().name());
        System.out.println("工具描述: " + contextQATool.getToolDefinition().description());
        System.out.println("输入 Schema: " + contextQATool.getToolDefinition().inputSchema());
        System.out.println("=== 演示完成 ===");
    }

    @Test
    void demonstrateToolInputSchemaGeneration() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);
        
        // 创建配置工具生成器
        ConfigBasedToolGenerator generator = new ConfigBasedToolGenerator(mockChatClient);
        
        // 生成 context_qa 工具
        List<ToolCallback> toolCallbacks = generator.generateToolCallbacks("context_qa");
        
        assertNotNull(toolCallbacks);
        assertEquals(1, toolCallbacks.size());
        
        ToolCallback contextQATool = toolCallbacks.get(0);
        String inputSchema = contextQATool.getToolDefinition().inputSchema();
        
        assertNotNull(inputSchema);
        
        // 验证 JSON Schema 包含必要的字段
        assertTrue(inputSchema.contains("input"), "Schema should contain 'input' field");
        assertTrue(inputSchema.contains("query"), "Schema should contain 'query' field");
        assertTrue(inputSchema.contains("required"), "Schema should contain 'required' array");
        assertTrue(inputSchema.contains("交互内容中的历史对话或需要问答的文本"), "Schema should contain input description");
        assertTrue(inputSchema.contains("用户的问题或需求"), "Schema should contain query description");
        
        System.out.println("=== 工具输入 Schema 演示 ===");
        System.out.println("生成的 JSON Schema:");
        System.out.println(inputSchema);
        System.out.println("=== Schema 演示完成 ===");
    }
}
