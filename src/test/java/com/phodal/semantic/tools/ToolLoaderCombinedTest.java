package com.phodal.semantic.tools;

import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallback;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 测试 ToolLoader 的组合工具加载功能
 */
class ToolLoaderCombinedTest {

    @Test
    void testPlanBusinessWithCombinedTools() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);
        
        // 创建配置工具生成器
        ConfigBasedToolGenerator configBasedToolGenerator = new ConfigBasedToolGenerator(mockChatClient);
        
        // 创建工具加载器
        ToolLoader toolLoader = new ToolLoader(configBasedToolGenerator);
        
        // 测试加载 plan 业务的工具
        List<ToolCallback> tools = toolLoader.getAllAvailableTools("plan");
        
        // 验证结果
        assertNotNull(tools);
        assertFalse(tools.isEmpty());
        
        // 应该包含通用工具、规划工具和配置生成的工具
        System.out.println("=== Plan 业务工具加载测试 ===");
        System.out.println("总工具数量: " + tools.size());
        
        // 检查是否包含规划工具
        boolean hasPlanningTools = tools.stream()
                .anyMatch(tool -> tool.getToolDefinition().description().contains("规划") ||
                                tool.getToolDefinition().description().contains("计划"));
        
        // 检查是否包含通用工具
        boolean hasGeneralTools = tools.stream()
                .anyMatch(tool -> tool.getToolDefinition().description().contains("总结") ||
                                tool.getToolDefinition().description().contains("翻译"));
        
        // 检查是否包含配置生成的工具（如果存在 plan 配置）
        boolean hasConfigBasedTools = tools.stream()
                .anyMatch(tool -> "plan".equals(tool.getToolDefinition().name()));
        
        System.out.println("包含规划工具: " + hasPlanningTools);
        System.out.println("包含通用工具: " + hasGeneralTools);
        System.out.println("包含配置生成工具: " + hasConfigBasedTools);
        
        // 打印所有工具
        System.out.println("所有工具列表:");
        tools.forEach(tool -> 
            System.out.println("  - " + tool.getToolDefinition().name() + ": " + 
                             tool.getToolDefinition().description()));
        
        assertTrue(hasGeneralTools, "Should contain general tools");
        System.out.println("=== 测试完成 ===");
    }

    @Test
    void testContextQABusinessWithConfigTools() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);
        
        // 创建配置工具生成器
        ConfigBasedToolGenerator configBasedToolGenerator = new ConfigBasedToolGenerator(mockChatClient);
        
        // 创建工具加载器
        ToolLoader toolLoader = new ToolLoader(configBasedToolGenerator);
        
        // 测试加载 context_qa 业务的工具
        List<ToolCallback> tools = toolLoader.getAllAvailableTools("context_qa");
        
        // 验证结果
        assertNotNull(tools);
        assertFalse(tools.isEmpty());
        
        // 应该包含通用工具和配置生成的 context_qa 工具
        boolean hasContextQATool = tools.stream()
                .anyMatch(tool -> "context_qa".equals(tool.getToolDefinition().name()));
        
        boolean hasGeneralTools = tools.stream()
                .anyMatch(tool -> tool.getToolDefinition().description().contains("总结") ||
                                tool.getToolDefinition().description().contains("翻译"));
        
        System.out.println("=== Context QA 业务工具加载测试 ===");
        System.out.println("总工具数量: " + tools.size());
        System.out.println("包含 context_qa 工具: " + hasContextQATool);
        System.out.println("包含通用工具: " + hasGeneralTools);
        
        assertTrue(hasContextQATool, "Should contain context_qa tool");
        assertTrue(hasGeneralTools, "Should contain general tools");
        System.out.println("=== 测试完成 ===");
    }

    @Test
    void testUnknownBusinessWithFallback() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);
        
        // 创建配置工具生成器
        ConfigBasedToolGenerator configBasedToolGenerator = new ConfigBasedToolGenerator(mockChatClient);
        
        // 创建工具加载器
        ToolLoader toolLoader = new ToolLoader(configBasedToolGenerator);
        
        // 测试加载未知业务的工具
        List<ToolCallback> tools = toolLoader.getAllAvailableTools("unknown_business");
        
        // 验证结果
        assertNotNull(tools);
        assertFalse(tools.isEmpty());
        
        // 应该只包含通用工具（没有传统工具，也没有配置生成的工具）
        boolean hasGeneralTools = tools.stream()
                .anyMatch(tool -> tool.getToolDefinition().description().contains("总结") ||
                                tool.getToolDefinition().description().contains("翻译"));
        
        boolean hasUnknownBusinessTool = tools.stream()
                .anyMatch(tool -> "unknown_business".equals(tool.getToolDefinition().name()));
        
        System.out.println("=== 未知业务工具加载测试 ===");
        System.out.println("总工具数量: " + tools.size());
        System.out.println("包含通用工具: " + hasGeneralTools);
        System.out.println("包含未知业务工具: " + hasUnknownBusinessTool);
        
        assertTrue(hasGeneralTools, "Should contain general tools");
        assertFalse(hasUnknownBusinessTool, "Should not contain unknown_business tool");
        System.out.println("=== 测试完成 ===");
    }
}
