package com.phodal.semantic.tools;

import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallback;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ConfigBasedToolGeneratorTest {

    @Test
    void testGenerateToolCallbacks() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);
        
        // 创建配置工具生成器
        ConfigBasedToolGenerator generator = new ConfigBasedToolGenerator(mockChatClient);
        
        // 测试生成 context_qa 工具
        List<ToolCallback> toolCallbacks = generator.generateToolCallbacks("context_qa");
        
        // 验证结果
        assertNotNull(toolCallbacks);
        assertEquals(1, toolCallbacks.size());
        
        ToolCallback toolCallback = toolCallbacks.get(0);
        assertNotNull(toolCallback);
        assertNotNull(toolCallback.getToolDefinition());
        assertNotNull(toolCallback.getToolMetadata());
        
        // 验证工具定义
        assertEquals("context_qa", toolCallback.getToolDefinition().name());
        assertEquals("交互内容中存在和问题相关的信息时，请调用这个函数，根据交互内容或给出的文本，回答用户的问题", 
                     toolCallback.getToolDefinition().description());
        
        // 验证输入参数 schema
        String inputSchema = toolCallback.getToolDefinition().inputSchema();
        assertNotNull(inputSchema);
        assertTrue(inputSchema.contains("input"));
        assertTrue(inputSchema.contains("query"));
    }

    @Test
    void testGenerateToolCallbacksForNonExistentBusiness() {
        // 创建模拟的 ChatClient
        ChatClient mockChatClient = mock(ChatClient.class);
        
        // 创建配置工具生成器
        ConfigBasedToolGenerator generator = new ConfigBasedToolGenerator(mockChatClient);
        
        // 测试生成不存在的业务工具
        List<ToolCallback> toolCallbacks = generator.generateToolCallbacks("non_existent");
        
        // 验证结果
        assertNotNull(toolCallbacks);
        assertTrue(toolCallbacks.isEmpty());
    }
}
