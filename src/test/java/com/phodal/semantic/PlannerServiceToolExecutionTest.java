package com.phodal.semantic;

import com.phodal.semantic.controller.dto.PlanInfo;
import com.phodal.semantic.model.DynamicModelService;
import com.phodal.semantic.template.PromptTemplateManager;
import com.phodal.semantic.tools.ToolLoader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.definition.ToolDefinition;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 测试 PlannerService 的工具执行功能
 */
class PlannerServiceToolExecutionTest {

    @Mock
    private DynamicModelService dynamicModelService;

    @Mock
    private ToolLoader toolLoader;

    @Mock
    private PromptTemplateManager templateManager;

    @Mock
    private ChatClient chatClient;

    private PlannerService plannerService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        plannerService = new PlannerService(dynamicModelService, toolLoader, templateManager);
    }

    @Test
    void testParseResponseWithSingleFunctionName() {
        // 测试解析单一函数名（如 contextQa）的响应
        String mockResponse = """
            {"plan":{"reason":"用户询问关于外代销产品数据下发时间的问题，可以使用函数列表中的问答函数",
            "function":"contextQa",
            "parameters":{"input":"外代销产品，请问给代销机构下发数据一般是几点？"}
            }}
            #END-OF-PLAN
            """;

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method parseResponseMethod = PlannerService.class.getDeclaredMethod(
                "parseResponse", String.class, String.class, String.class);
            parseResponseMethod.setAccessible(true);
            
            PlannerService.PlanTaskResult result = (PlannerService.PlanTaskResult) parseResponseMethod.invoke(
                plannerService, mockResponse, "test input", "test-session");
            
            assertNotNull(result);
            assertNotNull(result.getPlans());
            assertEquals(1, result.getPlans().size());
            
            PlanInfo plan = result.getPlans().get(0);
            assertEquals("", plan.getPluginName()); // 空插件名
            assertEquals("contextQa", plan.getName());
            assertNotNull(plan.getVariables());
            assertTrue(plan.getVariables().containsKey("input"));
            
        } catch (Exception e) {
            fail("Failed to test parseResponse method: " + e.getMessage());
        }
    }

    @Test
    void testParseResponseWithPluginFunctionName() {
        // 测试解析带插件名的函数（如 order_plugin.inquiry_order_price）的响应
        String mockResponse = """
            {"plan":{"reason":"用户最新的要求是询价，我可以使用函数列表中的询价函数",
            "function":"order_plugin.inquiry_order_price",
            "parameters":{"input":"询：600105永鼎股份，虚值5%看涨，2个月，300万名义本金"}
            }}
            #END-OF-PLAN
            """;

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method parseResponseMethod = PlannerService.class.getDeclaredMethod(
                "parseResponse", String.class, String.class, String.class);
            parseResponseMethod.setAccessible(true);
            
            PlannerService.PlanTaskResult result = (PlannerService.PlanTaskResult) parseResponseMethod.invoke(
                plannerService, mockResponse, "test input", "test-session");
            
            assertNotNull(result);
            assertNotNull(result.getPlans());
            assertEquals(1, result.getPlans().size());
            
            PlanInfo plan = result.getPlans().get(0);
            assertEquals("order_plugin", plan.getPluginName());
            assertEquals("inquiry_order_price", plan.getName());
            assertNotNull(plan.getVariables());
            assertTrue(plan.getVariables().containsKey("input"));
            
        } catch (Exception e) {
            fail("Failed to test parseResponse method: " + e.getMessage());
        }
    }

    @Test
    void testExecuteToolPlansWithSingleFunctionName() {
        // 测试执行单一函数名的工具
        List<PlanInfo> plans = new ArrayList<>();
        Map<String, Object> variables = new HashMap<>();
        variables.put("input", "test input");
        
        PlanInfo plan = new PlanInfo("", "contextQa", "test reason", variables);
        plans.add(plan);

        // 创建 mock 工具
        ToolCallback mockTool = mock(ToolCallback.class);
        when(mockTool.getToolDefinition()).thenReturn(
            ToolDefinition.builder()
                .name("contextQa")
                .description("Context QA tool")
                .build()
        );
        when(mockTool.call(anyString())).thenReturn("Mock tool result");

        List<ToolCallback> tools = List.of(mockTool);
        when(toolLoader.getAllAvailableTools("plan")).thenReturn(tools);

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method executeToolPlansMethod = PlannerService.class.getDeclaredMethod(
                "executeToolPlans", List.class, String.class, String.class);
            executeToolPlansMethod.setAccessible(true);
            
            String result = (String) executeToolPlansMethod.invoke(
                plannerService, plans, "plan", "test-session");
            
            assertNotNull(result);
            assertEquals("Mock tool result", result);
            
            // 验证工具被调用
            verify(mockTool).call(anyString());
            
        } catch (Exception e) {
            fail("Failed to test executeToolPlans method: " + e.getMessage());
        }
    }

    @Test
    void testExecuteToolPlansWithPluginFunctionName() {
        // 测试执行带插件名的工具
        List<PlanInfo> plans = new ArrayList<>();
        Map<String, Object> variables = new HashMap<>();
        variables.put("input", "test input");
        
        PlanInfo plan = new PlanInfo("order_plugin", "inquiry_order_price", "test reason", variables);
        plans.add(plan);

        // 创建 mock 工具
        ToolCallback mockTool = mock(ToolCallback.class);
        when(mockTool.getToolDefinition()).thenReturn(
            ToolDefinition.builder()
                .name("order_plugin.inquiry_order_price")
                .description("Order inquiry tool")
                .build()
        );
        when(mockTool.call(anyString())).thenReturn("Mock order result");

        List<ToolCallback> tools = List.of(mockTool);
        when(toolLoader.getAllAvailableTools("plan")).thenReturn(tools);

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method executeToolPlansMethod = PlannerService.class.getDeclaredMethod(
                "executeToolPlans", List.class, String.class, String.class);
            executeToolPlansMethod.setAccessible(true);
            
            String result = (String) executeToolPlansMethod.invoke(
                plannerService, plans, "plan", "test-session");
            
            assertNotNull(result);
            assertEquals("Mock order result", result);
            
            // 验证工具被调用
            verify(mockTool).call(anyString());
            
        } catch (Exception e) {
            fail("Failed to test executeToolPlans method: " + e.getMessage());
        }
    }

    @Test
    void testParseResponseWithNullFunction() {
        // 测试解析 function 为 null 的响应
        String mockResponse = """
            {"plan":{"reason":"用户想要听笑话，但是函数列表不包含讲笑话或有趣的东西的功能",
            "function": null,
            "parameters": {}
            }}
            #END-OF-PLAN
            """;

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method parseResponseMethod = PlannerService.class.getDeclaredMethod(
                "parseResponse", String.class, String.class, String.class);
            parseResponseMethod.setAccessible(true);
            
            PlannerService.PlanTaskResult result = (PlannerService.PlanTaskResult) parseResponseMethod.invoke(
                plannerService, mockResponse, "test input", "test-session");
            
            assertNotNull(result);
            assertNotNull(result.getPlans());
            assertEquals(0, result.getPlans().size()); // 没有计划被创建
            
        } catch (Exception e) {
            fail("Failed to test parseResponse method: " + e.getMessage());
        }
    }
}
