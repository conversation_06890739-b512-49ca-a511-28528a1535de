package com.phodal.semantic.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phodal.semantic.controller.dto.Message;
import com.phodal.semantic.controller.dto.PlanTaskRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.*;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
public class SemanticControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testPlanTaskEndpointIntegration() throws Exception {
        // 准备测试数据
        PlanTaskRequest request = new PlanTaskRequest();
        
        List<Message> messages = Arrays.asList(
            new Message("user", "请帮我规划一下今天的任务")
        );
        request.setMessages(messages);
        
        Map<String, Object> juziData = new HashMap<>();
        juziData.put("chatId", "integration_test_123");
        request.setJuziData(juziData);

        // 执行测试 - 这个测试可能会失败，因为需要真实的LLM配置
        // 但它可以验证API结构是否正确
        mockMvc.perform(post("/api/v1/plan_task")
                .header("user-token", "integration_test_token")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists())
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    void testPlanTaskWithInvalidRequest() throws Exception {
        // 测试空消息列表
        PlanTaskRequest request = new PlanTaskRequest();
        request.setMessages(Collections.emptyList());

        mockMvc.perform(post("/api/v1/plan_task")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(-1));
    }

    @Test
    void testBackwardCompatibilityEndpoints() throws Exception {
        // 测试向后兼容的API端点
        Map<String, String> legacyRequest = new HashMap<>();
        legacyRequest.put("input", "测试输入");
        legacyRequest.put("businessType", "plan");

        mockMvc.perform(post("/api/semantic/plan")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(legacyRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").exists());
    }
}
