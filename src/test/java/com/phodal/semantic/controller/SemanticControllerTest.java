package com.phodal.semantic.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phodal.semantic.PlannerService;
import com.phodal.semantic.config.ConfigurationManager;
import com.phodal.semantic.controller.dto.Message;
import com.phodal.semantic.controller.dto.PlanInfo;
import com.phodal.semantic.controller.dto.PlanTaskRequest;
import com.phodal.semantic.model.DynamicModelService;
import com.phodal.semantic.tools.ToolLoader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(SemanticController.class)
public class SemanticControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PlannerService plannerService;

    @MockBean
    private DynamicModelService dynamicModelService;

    @MockBean
    private ToolLoader toolLoader;

    @MockBean
    private ConfigurationManager configurationManager;

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    void testPlanTaskEndpoint() throws Exception {
        // 准备测试数据
        PlanTaskRequest request = new PlanTaskRequest();
        
        List<Message> messages = Arrays.asList(
            new Message("user", "外代销产品，请问给代销机构下发数据一般是几点？")
        );
        request.setMessages(messages);
        
        Map<String, Object> juziData = new HashMap<>();
        juziData.put("chatId", "123456");
        juziData.put("other_key", "other_value");
        request.setJuziData(juziData);

        // 模拟服务响应
        List<PlanInfo> plans = Arrays.asList(
            new PlanInfo("general_plugins", "context_qa", 
                "交互内容中存在和问题相关的信息时，请调用这个函数，根据交互内容或给出的文本，回答用户的问题",
                Map.of("input", "外代销产品,请问给代销机构下发数据一般是几点?", 
                       "query", "外代销产品,请问给代销机构下发数据一般是几点?"))
        );
        
        String answer = "给代销机构下发数据的具体时间通常由合作双方的协议或系统对接规则决定，以下是一般情况下的参考信息：\n\n1. **常见时间段**  \n   - 多数机构选择在 **交易日收盘后（15:00-18:00）** 或 **晚间（20:00-24:00）** 下发数据，以确保当日交易数据完整。\n   - 部分机构可能采用 **次日凌晨（如2:00-5:00）** 批量处理模式。\n\n2. **关键影响因素**  \n   - 数据来源方的清算完成时间（如基金公司TA系统结算时间）。  \n   - 代销机构的系统接收窗口（需避开其系统维护时段）。\n\n3. **建议确认方式**  \n   - 直接联系对接的业务经理或技术支持，获取协议中约定的具体时间表。  \n   - 查看双方签署的《代销服务协议》技术附件（通常包含数据交互时间条款）。  \n\n若您需要更具体的回答，请提供代销产品类型（如公募基金、信托等）或机构名称，可进一步帮助核实行业惯例。";

        PlannerService.PlanTaskResult mockResult = new PlannerService.PlanTaskResult(plans, answer);
        when(plannerService.planTask(any(PlanTaskRequest.class), anyString())).thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(post("/api/v1/plan_task")
                .header("user-token", "your_user_token_here")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.plans").isArray())
                .andExpect(jsonPath("$.data.plans[0].plugin_name").value("general_plugins"))
                .andExpect(jsonPath("$.data.plans[0].name").value("context_qa"))
                .andExpect(jsonPath("$.data.answer").isString());
    }

    @Test
    void testPlanTaskWithoutUserToken() throws Exception {
        // 准备测试数据
        PlanTaskRequest request = new PlanTaskRequest();
        
        List<Message> messages = Arrays.asList(
            new Message("user", "测试消息")
        );
        request.setMessages(messages);

        // 模拟服务响应
        PlannerService.PlanTaskResult mockResult = new PlannerService.PlanTaskResult(
            Collections.emptyList(), "测试回答");
        when(plannerService.planTask(any(PlanTaskRequest.class), anyString())).thenReturn(mockResult);

        // 执行测试（不提供user-token）
        mockMvc.perform(post("/api/v1/plan_task")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("success"));
    }

    @Test
    void testPlanTaskErrorHandling() throws Exception {
        // 准备测试数据
        PlanTaskRequest request = new PlanTaskRequest();
        
        List<Message> messages = Arrays.asList(
            new Message("user", "测试消息")
        );
        request.setMessages(messages);

        // 模拟服务抛出异常
        when(plannerService.planTask(any(PlanTaskRequest.class), anyString()))
            .thenThrow(new RuntimeException("测试异常"));

        // 执行测试
        mockMvc.perform(post("/api/v1/plan_task")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(-1))
                .andExpect(jsonPath("$.message").value("处理请求时发生错误：测试异常"));
    }
}
