package com.phodal.semantic;

import com.phodal.semantic.controller.dto.PlanTaskRequest;
import com.phodal.semantic.model.DynamicModelService;
import com.phodal.semantic.template.PromptTemplateManager;
import com.phodal.semantic.tools.ToolLoader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallback;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 测试 PlannerService 的新的基于 prompt 的实现
 */
class PlannerServicePromptBasedTest {

    @Mock
    private DynamicModelService dynamicModelService;

    @Mock
    private ToolLoader toolLoader;

    @Mock
    private PromptTemplateManager templateManager;

    @Mock
    private ChatClient chatClient;

    private PlannerService plannerService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        plannerService = new PlannerService(dynamicModelService, toolLoader, templateManager);
    }

    @Test
    void testPlannerServiceCreation() {
        // 简单测试 PlannerService 可以正常创建
        assertNotNull(plannerService);
    }

    @Test
    void testGetAvailableTools() {
        // 测试获取可用工具列表
        String businessType = "plan";
        List<ToolCallback> mockTools = new ArrayList<>();

        when(toolLoader.getAllAvailableTools(businessType)).thenReturn(mockTools);

        String result = plannerService.getAvailableTools(businessType);

        assertNotNull(result);
        assertTrue(result.contains("可用工具列表"));
        verify(toolLoader).getAllAvailableTools(businessType);
    }

    @Test
    void testReloadPromptTemplate() {
        // 测试重新加载提示词模板
        plannerService.reloadPromptTemplate();

        // 验证调用
        verify(templateManager).reloadTemplate("plan/skprompt.txt");
    }
}
