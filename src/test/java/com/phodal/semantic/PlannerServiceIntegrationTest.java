package com.phodal.semantic;

import com.phodal.semantic.controller.dto.Message;
import com.phodal.semantic.controller.dto.PlanTaskRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PlannerService 集成测试
 * 测试新的基于 prompt 的实现在真实环境中的工作情况
 */
@SpringBootTest
@ActiveProfiles("test")
class PlannerServiceIntegrationTest {

    @Autowired
    private PlannerService plannerService;

    @Test
    void testPlannerServiceIsAvailable() {
        // 验证 PlannerService 可以正常注入
        assertNotNull(plannerService);
    }

    @Test
    void testGetAvailableToolsForDifferentBusinessTypes() {
        // 测试不同业务类型的工具列表
        String[] businessTypes = {"plan", "qa", "compliance_review"};
        
        for (String businessType : businessTypes) {
            String tools = plannerService.getAvailableTools(businessType);
            assertNotNull(tools, "Tools should not be null for business type: " + businessType);
            assertTrue(tools.contains("可用工具列表"), "Should contain tools list header");
        }
    }

    @Test
    void testReloadPromptTemplate() {
        // 测试重新加载提示词模板
        assertDoesNotThrow(() -> {
            plannerService.reloadPromptTemplate();
        });
    }

    @Test
    void testPlanTaskWithEmptyRequest() {
        // 测试空请求的处理
        PlanTaskRequest request = new PlanTaskRequest();
        request.setBusinessType("plan");
        
        PlannerService.PlanTaskResult result = plannerService.planTask(request, "test-token");
        
        assertNotNull(result);
        assertNotNull(result.getAnswer());
        assertTrue(result.getAnswer().contains("请提供有效的用户消息"));
    }

    @Test
    void testPlanWithEmptyInput() {
        // 测试空输入的处理
        String result = plannerService.plan("", "plan");
        
        assertNotNull(result);
        assertTrue(result.contains("请提供有效的输入内容"));
    }

    @Test
    void testPlanWithNullInput() {
        // 测试 null 输入的处理
        String result = plannerService.plan(null, "plan");
        
        assertNotNull(result);
        assertTrue(result.contains("请提供有效的输入内容"));
    }

    @Test
    void testPlanWithCustomPrompt() {
        // 测试自定义 prompt
        String customPrompt = "你是一个测试助手，请直接回复：测试成功";
        String result = plannerService.planWithCustomPrompt("测试", "plan", customPrompt);
        
        assertNotNull(result);
        // 由于没有真实的 LLM 连接，这里只验证方法不抛异常
    }

    @Test
    void testPlanTaskResultStructure() {
        // 测试 PlanTaskResult 的结构
        PlanTaskRequest request = new PlanTaskRequest();
        request.setMessages(List.of(
            new Message("user", "测试消息")
        ));
        request.setBusinessType("plan");
        
        PlannerService.PlanTaskResult result = plannerService.planTask(request, "test-token");
        
        assertNotNull(result);
        assertNotNull(result.getPlans());
        assertNotNull(result.getAnswer());
        
        // 验证 getter 方法正常工作
        assertTrue(result.getPlans() instanceof List);
        assertTrue(result.getAnswer() instanceof String);
    }

    @Test
    void testDifferentBusinessTypes() {
        // 测试不同的业务类型
        String[] businessTypes = {"plan", "qa", "compliance_review", "unknown"};
        
        for (String businessType : businessTypes) {
            assertDoesNotThrow(() -> {
                String result = plannerService.plan("测试输入", businessType);
                assertNotNull(result);
            }, "Should not throw exception for business type: " + businessType);
        }
    }

    @Test
    void testPlanTaskWithDifferentMessageFormats() {
        // 测试不同的消息格式
        PlanTaskRequest request1 = new PlanTaskRequest();
        request1.setMessages(List.of(
            new Message("system", "系统消息"),
            new Message("user", "用户消息")
        ));
        request1.setBusinessType("plan");
        
        PlannerService.PlanTaskResult result1 = plannerService.planTask(request1, "test-token");
        assertNotNull(result1);
        
        // 测试只有用户消息的情况
        PlanTaskRequest request2 = new PlanTaskRequest();
        request2.setMessages(List.of(
            new Message("user", "只有用户消息")
        ));
        request2.setBusinessType("plan");
        
        PlannerService.PlanTaskResult result2 = plannerService.planTask(request2, "test-token");
        assertNotNull(result2);
    }
}
