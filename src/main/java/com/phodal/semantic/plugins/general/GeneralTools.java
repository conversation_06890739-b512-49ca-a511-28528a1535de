package com.phodal.semantic.plugins.general;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 通用工具类，提供基础的工具函数
 */
public class GeneralTools {
    
    @Tool(description = "总结输入的文本。如果用户需要，可以从交互内容中提取信息并总结")
    public String summarize(@ToolParam(description = "要总结的文本") String input) {
        if (input == null || input.trim().isEmpty()) {
            return "没有提供需要总结的内容。";
        }
        
        // 简单的总结逻辑
        String[] sentences = input.split("[。！？.!?]");
        if (sentences.length <= 2) {
            return "内容较短，总结如下：" + input.trim();
        }
        
        // 取前两句作为总结
        String summary = Arrays.stream(sentences)
                .limit(2)
                .collect(Collectors.joining("。"));
        
        return "总结：" + summary + "。";
    }
    
    @Tool(description = "将输入的文本翻译成您选择的语言")
    public String translate(
            @ToolParam(description = "要翻译的文本") String input,
            @ToolParam(description = "目标语言") String language) {
        
        if (input == null || input.trim().isEmpty()) {
            return "没有提供需要翻译的内容。";
        }
        
        // 这里是一个简化的翻译示例，实际应该调用翻译API
        return String.format("已将文本翻译为%s：%s", language, input);
    }
    
    @Tool(description = "将要点变成发给某人的电子邮件内容，使用礼貌的语气。注意！如果历史对话已经生成了符合要求的邮件内容，请不要再次调用此函数重复生成邮件内容")
    public String emailGen(
            @ToolParam(description = "邮件要点") String input,
            @ToolParam(description = "寄件人") String sender,
            @ToolParam(description = "收件人") String to) {
        
        if (input == null || input.trim().isEmpty()) {
            return "没有提供邮件要点。";
        }
        
        StringBuilder email = new StringBuilder();
        email.append("亲爱的 ").append(to).append("：\n\n");
        email.append("希望这封邮件能够找到您身体健康。\n\n");
        email.append(input).append("\n\n");
        email.append("如有任何问题，请随时与我联系。\n\n");
        email.append("此致\n敬礼\n\n");
        email.append(sender);
        
        return email.toString();
    }
    
    @Tool(description = "根据输入的场景写成一首诗")
    public String shortPoem(@ToolParam(description = "要写成一首诗的场景") String input) {
        if (input == null || input.trim().isEmpty()) {
            return "没有提供诗歌场景。";
        }
        
        // 简单的诗歌生成示例
        return String.format("关于%s的诗：\n\n%s如画卷，\n美景入心田。\n诗意满人间，\n情深意更绵。", input, input);
    }
    
    @Tool(description = "给定目标或主题描述，生成想法列表")
    public String brainstorm(@ToolParam(description = "目标或主题描述") String input) {
        if (input == null || input.trim().isEmpty()) {
            return "没有提供头脑风暴主题。";
        }
        
        StringBuilder ideas = new StringBuilder();
        ideas.append("关于 \"").append(input).append("\" 的想法列表：\n\n");
        ideas.append("1. 深入分析现状和背景\n");
        ideas.append("2. 收集相关数据和信息\n");
        ideas.append("3. 寻找创新解决方案\n");
        ideas.append("4. 评估可行性和风险\n");
        ideas.append("5. 制定实施计划\n");
        ideas.append("6. 建立反馈机制\n");
        ideas.append("7. 持续优化改进\n");
        
        return ideas.toString();
    }
    
    @Tool(description = "交互内容中存在和问题相关的信息时，请调用这个函数，根据交互内容或给出的文本，回答用户的问题")
    public String contextQa(
            @ToolParam(description = "交互内容中的历史对话或需要问答的文本") String input,
            @ToolParam(description = "用户的问题或需求") String query) {
        
        if (query == null || query.trim().isEmpty()) {
            return "没有提供用户问题。";
        }
        
        if (input == null || input.trim().isEmpty()) {
            return "没有提供上下文信息，无法基于历史对话回答问题。";
        }
        
        // 简单的上下文问答逻辑
        return String.format("基于提供的上下文信息，针对问题 \"%s\" 的回答：\n\n根据上下文内容分析，%s", 
                query, "请参考相关信息进行判断。");
    }
    
    @Tool(description = "当客户的问题只是闲聊、问候、感谢或简单回复，请调用此函数和用户聊天或向用户问好")
    public String chat(@ToolParam(description = "客户的闲聊，或是问候、感谢或简单回复，例如你好、谢谢等") String input) {
        if (input == null || input.trim().isEmpty()) {
            return "您好！有什么我可以帮助您的吗？";
        }
        
        String lowerInput = input.toLowerCase().trim();
        
        if (lowerInput.contains("你好") || lowerInput.contains("hello") || lowerInput.contains("hi")) {
            return "您好！很高兴为您服务，有什么我可以帮助您的吗？";
        } else if (lowerInput.contains("谢谢") || lowerInput.contains("thank")) {
            return "不客气！如果还有其他需要帮助的地方，请随时告诉我。";
        } else if (lowerInput.contains("再见") || lowerInput.contains("bye")) {
            return "再见！祝您有美好的一天！";
        } else {
            return "我理解您的意思。有什么具体的问题我可以帮助您解决吗？";
        }
    }
    
    @Tool(description = "获取当前日期和时间")
    public String getCurrentDateTime() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss");
        return "当前时间：" + now.format(formatter);
    }
}
