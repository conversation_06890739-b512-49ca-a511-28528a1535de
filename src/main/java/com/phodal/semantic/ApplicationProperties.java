package com.phodal.semantic;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;

@Data
@ConfigurationProperties(prefix = "app")
public class ApplicationProperties {
    private MiscConfig misc;

    @Data
    public static class MiscConfig {
        private String glebaBaseUrl;
        private String esbBaseUrl;
        private String esbUser;
        private String esbPasswd;
    }
}