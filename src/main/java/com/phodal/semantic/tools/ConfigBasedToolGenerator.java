package com.phodal.semantic.tools;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phodal.semantic.config.BusinessConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.definition.ToolDefinition;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 配置工具生成器，从配置文件生成 Spring AI 的 ToolCallback
 */
@Component
public class ConfigBasedToolGenerator {

    private static final Logger logger = LoggerFactory.getLogger(ConfigBasedToolGenerator.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ChatClient chatClient;

    public ConfigBasedToolGenerator(ChatClient chatClient) {
        this.chatClient = chatClient;
    }

    /**
     * 从配置目录生成工具回调
     * @param businessName 业务名称（对应配置目录名）
     * @return 生成的工具回调列表
     */
    public List<ToolCallback> generateToolCallbacks(String businessName) {
        List<ToolCallback> toolCallbacks = new ArrayList<>();
        
        try {
            // 加载配置文件
            BusinessConfig config = loadBusinessConfig(businessName);
            if (config == null) {
                logger.warn("No configuration found for business: {}", businessName);
                return toolCallbacks;
            }

            // 加载提示词模板
            String promptTemplate = loadPromptTemplate(businessName);
            if (promptTemplate == null) {
                logger.warn("No prompt template found for business: {}", businessName);
                return toolCallbacks;
            }

            // 创建工具回调
            ToolCallback toolCallback = createToolCallback(businessName, config, promptTemplate);
            toolCallbacks.add(toolCallback);

            logger.info("Generated tool callback for business: {}", businessName);
            
        } catch (Exception e) {
            logger.error("Failed to generate tool callbacks for business: {}", businessName, e);
        }

        return toolCallbacks;
    }

    /**
     * 加载业务配置
     */
    private BusinessConfig loadBusinessConfig(String businessName) {
        try {
            ClassPathResource resource = new ClassPathResource(businessName + "/config.json");
            if (!resource.exists()) {
                return null;
            }
            
            try (InputStream inputStream = resource.getInputStream()) {
                return objectMapper.readValue(inputStream, BusinessConfig.class);
            }
        } catch (IOException e) {
            logger.error("Failed to load business config for: {}", businessName, e);
            return null;
        }
    }

    /**
     * 加载提示词模板
     */
    private String loadPromptTemplate(String businessName) {
        try {
            ClassPathResource resource = new ClassPathResource(businessName + "/skprompt.txt");
            if (!resource.exists()) {
                return null;
            }
            
            try (InputStream inputStream = resource.getInputStream()) {
                return new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            }
        } catch (IOException e) {
            logger.error("Failed to load prompt template for: {}", businessName, e);
            return null;
        }
    }

    /**
     * 创建工具回调
     */
    private ToolCallback createToolCallback(String businessName, BusinessConfig config, String promptTemplate) {
        return new ConfigBasedToolCallback(businessName, config, promptTemplate, chatClient);
    }

    /**
     * 基于配置的工具回调实现
     */
    private static class ConfigBasedToolCallback implements ToolCallback {
        
        private final String businessName;
        private final BusinessConfig config;
        private final String promptTemplate;
        private final ChatClient chatClient;
        private final ToolDefinition toolDefinition;
        private final ToolMetadata toolMetadata;

        public ConfigBasedToolCallback(String businessName, BusinessConfig config, String promptTemplate, ChatClient chatClient) {
            this.businessName = businessName;
            this.config = config;
            this.promptTemplate = promptTemplate;
            this.chatClient = chatClient;
            this.toolDefinition = createToolDefinition();
            this.toolMetadata = createToolMetadata();
        }

        @Override
        public ToolDefinition getToolDefinition() {
            return toolDefinition;
        }

        @Override
        public ToolMetadata getToolMetadata() {
            return toolMetadata;
        }

        @Override
        public String call(String toolInput) {
            try {
                // 解析工具输入参数
                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> inputParams = mapper.readValue(toolInput, Map.class);
                
                // 渲染提示词模板
                String renderedPrompt = renderPromptTemplate(inputParams);
                
                // 调用 ChatClient 生成响应
                String response = chatClient.prompt()
                        .user(renderedPrompt)
                        .call()
                        .content();
                
                return response;
                
            } catch (Exception e) {
                logger.error("Failed to execute tool for business: {}", businessName, e);
                return "工具执行失败：" + e.getMessage();
            }
        }

        /**
         * 渲染提示词模板
         */
        private String renderPromptTemplate(Map<String, Object> inputParams) {
            String rendered = promptTemplate;
            
            // 替换模板中的变量
            for (Map.Entry<String, Object> entry : inputParams.entrySet()) {
                String placeholder = "{{$" + entry.getKey() + "}}";
                String value = entry.getValue() != null ? entry.getValue().toString() : "";
                rendered = rendered.replace(placeholder, value);
            }
            
            return rendered;
        }

        /**
         * 创建工具定义
         */
        private ToolDefinition createToolDefinition() {
            return ToolDefinition.builder()
                    .name(businessName)
                    .description(config.getDescription())
                    .inputSchema(generateInputSchema())
                    .build();
        }

        /**
         * 创建工具元数据
         */
        private ToolMetadata createToolMetadata() {
            return ToolMetadata.builder()
                    .returnDirect(false)
                    .build();
        }

        /**
         * 生成输入参数的 JSON Schema
         */
        private String generateInputSchema() {
            Map<String, Object> schema = new HashMap<>();
            schema.put("type", "object");
            
            Map<String, Object> properties = new HashMap<>();
            List<String> required = new ArrayList<>();
            
            if (config.getInputVariables() != null) {
                for (BusinessConfig.InputVariable variable : config.getInputVariables()) {
                    Map<String, Object> property = new HashMap<>();
                    property.put("type", "string");
                    property.put("description", variable.getDescription());

                    properties.put(variable.getName(), property);

                    if (variable.getIsRequired() != null && variable.getIsRequired()) {
                        required.add(variable.getName());
                    }
                }
            }
            
            schema.put("properties", properties);
            if (!required.isEmpty()) {
                schema.put("required", required);
            }
            
            try {
                return new ObjectMapper().writeValueAsString(schema);
            } catch (Exception e) {
                logger.error("Failed to generate input schema", e);
                return "{}";
            }
        }
    }
}
