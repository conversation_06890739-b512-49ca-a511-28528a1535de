package com.phodal.semantic.tools;

import com.phodal.semantic.plugins.general.ComplianceTools;
import com.phodal.semantic.plugins.general.GeneralTools;
import com.phodal.semantic.plugins.general.PlanningTools;
import com.phodal.semantic.plugins.general.QATools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 工具加载器，负责动态加载和管理工具函数
 * 使用 Spring AI 的 MethodToolCallbackProvider 来处理工具注册
 */
@Component
public class ToolLoader {

    private static final Logger logger = LoggerFactory.getLogger(ToolLoader.class);
    private final Map<String, ToolCallbackProvider> businessToolsCache = new ConcurrentHashMap<>();
    private final List<ToolCallbackProvider> generalToolProviders = new ArrayList<>();
    private final ConfigBasedToolGenerator configBasedToolGenerator;

    public ToolLoader(ConfigBasedToolGenerator configBasedToolGenerator) {
        this.configBasedToolGenerator = configBasedToolGenerator;
        initializeGeneralTools();
    }
    
    /**
     * 初始化通用工具
     */
    private void initializeGeneralTools() {
        try {
            // 创建通用工具的 ToolCallbackProvider
            ToolCallbackProvider generalToolProvider = MethodToolCallbackProvider.builder()
                    .toolObjects(new GeneralTools())
                    .build();
            generalToolProviders.add(generalToolProvider);

            logger.info("Initialized {} general tool providers", generalToolProviders.size());
        } catch (Exception e) {
            logger.error("Failed to initialize general tools", e);
        }
    }

    /**
     * 获取指定业务的工具回调列表
     * @param businessName 业务名称
     * @return 工具回调列表
     */
    public List<ToolCallback> getToolsForBusiness(String businessName) {
        ToolCallbackProvider provider = businessToolsCache.computeIfAbsent(businessName, this::loadBusinessToolProvider);
        return provider != null ? Arrays.asList(provider.getToolCallbacks()) : new ArrayList<>();
    }

    /**
     * 获取所有通用工具回调
     * @return 通用工具回调列表
     */
    public List<ToolCallback> getGeneralTools() {
        List<ToolCallback> allGeneralTools = new ArrayList<>();
        for (ToolCallbackProvider provider : generalToolProviders) {
            allGeneralTools.addAll(Arrays.asList(provider.getToolCallbacks()));
        }
        return allGeneralTools;
    }

    /**
     * 获取所有可用工具（通用工具 + 业务特定工具）
     * @param businessName 业务名称
     * @return 所有可用工具回调列表
     */
    public List<ToolCallback> getAllAvailableTools(String businessName) {
        List<ToolCallback> allTools = new ArrayList<>(getGeneralTools());
        allTools.addAll(getToolsForBusiness(businessName));
        return allTools;
    }

    /**
     * 获取所有可用工具对象（通用工具 + 业务特定工具）
     * @param businessName 业务名称
     * @return 所有可用工具对象列表
     */
    public List<Object> getAllAvailableToolObjects(String businessName) {
        List<Object> allToolObjects = new ArrayList<>();

        // 添加通用工具对象
        allToolObjects.add(new GeneralTools());

        // 添加业务特定工具对象
        Object businessToolObject = getBusinessToolObject(businessName);
        if (businessToolObject != null) {
            allToolObjects.add(businessToolObject);
        }

        return allToolObjects;
    }

    /**
     * 获取业务特定的工具对象
     */
    private Object getBusinessToolObject(String businessName) {
        switch (businessName.toLowerCase()) {
            case "qa":
            case "tuoguan":
                return new QATools();
            case "compliance_review":
                return new ComplianceTools();
            case "plan":
                return new PlanningTools();
            case "context_qa":
                // 配置生成的工具不返回工具对象，而是通过 ToolCallback 处理
                return null;
            default:
                return null;
        }
    }
    
    /**
     * 为特定业务加载工具提供者
     */
    private ToolCallbackProvider loadBusinessToolProvider(String businessName) {
        try {
            Object toolObject = null;
            List<ToolCallback> allToolCallbacks = new ArrayList<>();

            // 首先尝试加载传统的注解式工具
            switch (businessName.toLowerCase()) {
                case "qa":
                case "tuoguan":
                    // 加载问答相关工具
                    toolObject = new QATools();
                    break;

                case "compliance_review":
                    // 加载合规审核工具
                    toolObject = new ComplianceTools();
                    break;

                case "plan":
                    // 加载规划工具
                    toolObject = new PlanningTools();
                    break;

                case "context_qa":
                    // 对于 context_qa，只使用配置生成的工具
                    return loadConfigBasedToolProvider(businessName);

                default:
                    // 对于未知业务类型，不加载传统工具
                    break;
            }

            // 如果有传统工具对象，添加到工具列表
            if (toolObject != null) {
                ToolCallbackProvider traditionalProvider = MethodToolCallbackProvider.builder()
                        .toolObjects(toolObject)
                        .build();
                allToolCallbacks.addAll(Arrays.asList(traditionalProvider.getToolCallbacks()));
            }

            // 尝试加载配置生成的工具作为补充
            List<ToolCallback> configBasedTools = configBasedToolGenerator.generateToolCallbacks(businessName);
            if (!configBasedTools.isEmpty()) {
                allToolCallbacks.addAll(configBasedTools);
                logger.info("Added {} config-based tools for business: {}", configBasedTools.size(), businessName);
            }

            // 对于 plan 业务类型，额外加载 context_qa 工具
            if ("plan".equals(businessName.toLowerCase())) {
                List<ToolCallback> contextQaTools = configBasedToolGenerator.generateToolCallbacks("context_qa");
                if (!contextQaTools.isEmpty()) {
                    allToolCallbacks.addAll(contextQaTools);
                    logger.info("Added {} context_qa tools for plan business", contextQaTools.size());
                }
            }

            // 如果没有任何工具，返回 null
            if (allToolCallbacks.isEmpty()) {
                logger.info("No specific tools found for business: {}", businessName);
                return null;
            }

            // 创建组合的工具提供者
            final List<ToolCallback> finalToolCallbacks = allToolCallbacks;
            ToolCallbackProvider combinedProvider = new ToolCallbackProvider() {
                @Override
                public ToolCallback[] getToolCallbacks() {
                    return finalToolCallbacks.toArray(new ToolCallback[0]);
                }
            };

            logger.info("Loaded combined tool provider for business: {} with {} tools (traditional: {}, config-based: {})",
                businessName, allToolCallbacks.size(),
                allToolCallbacks.size() - configBasedTools.size(), configBasedTools.size());

            return combinedProvider;

        } catch (Exception e) {
            logger.error("Failed to load tools for business: {}", businessName, e);
        }

        return null;
    }

    /**
     * 加载基于配置的工具提供者
     */
    private ToolCallbackProvider loadConfigBasedToolProvider(String businessName) {
        try {
            List<ToolCallback> configBasedTools = configBasedToolGenerator.generateToolCallbacks(businessName);
            if (configBasedTools.isEmpty()) {
                return null;
            }

            // 创建一个简单的 ToolCallbackProvider 实现
            return new ToolCallbackProvider() {
                @Override
                public ToolCallback[] getToolCallbacks() {
                    return configBasedTools.toArray(new ToolCallback[0]);
                }
            };

        } catch (Exception e) {
            logger.error("Failed to load config-based tool provider for: {}", businessName, e);
            return null;
        }
    }

    /**
     * 清除工具缓存
     */
    public void clearCache() {
        businessToolsCache.clear();
        logger.info("Tool cache cleared");
    }

    /**
     * 获取缓存的业务工具数量
     */
    public int getCacheSize() {
        return businessToolsCache.size();
    }

    /**
     * 注册自定义工具
     * @param businessName 业务名称
     * @param toolInstance 工具实例
     */
    public void registerCustomTool(String businessName, Object toolInstance) {
        try {
            ToolCallbackProvider customProvider = MethodToolCallbackProvider.builder()
                    .toolObjects(toolInstance)
                    .build();

            businessToolsCache.put(businessName, customProvider);

            logger.info("Registered custom tool provider for business: {} with {} tools",
                businessName, customProvider.getToolCallbacks().length);
        } catch (Exception e) {
            logger.error("Failed to register custom tool for business: {}", businessName, e);
        }
    }

    /**
     * 获取工具统计信息
     */
    public Map<String, Object> getToolStats() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("generalToolProviders", generalToolProviders.size());
        stats.put("businessToolProviders", businessToolsCache.size());

        int totalGeneralTools = 0;
        for (ToolCallbackProvider provider : generalToolProviders) {
            totalGeneralTools += provider.getToolCallbacks().length;
        }
        stats.put("totalGeneralTools", totalGeneralTools);

        int totalBusinessTools = 0;
        for (ToolCallbackProvider provider : businessToolsCache.values()) {
            totalBusinessTools += provider.getToolCallbacks().length;
        }
        stats.put("totalBusinessTools", totalBusinessTools);
        stats.put("totalTools", totalGeneralTools + totalBusinessTools);

        return stats;
    }
}
