package com.phodal.semantic.config;

import com.phodal.semantic.model.DynamicModelService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Semantic Migration 框架配置类
 */
@Configuration
@EnableConfigurationProperties
public class SemanticMigrationConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(SemanticMigrationConfig.class);
    
    /**
     * 配置管理器 Bean
     */
    @Bean
    @Primary
    public ConfigurationManager configurationManager() {
        logger.info("Initializing ConfigurationManager");
        return new ConfigurationManager();
    }

    /**
     * 动态模型服务 Bean
     */
    @Bean
    public DynamicModelService dynamicModelService() {
        logger.info("Initializing DynamicModelService");
        return new DynamicModelService();
    }

    /**
     * 默认 ChatClient Bean
     */
    @Bean
    @Primary
    public ChatClient chatClient(DynamicModelService dynamicModelService) {
        logger.info("Initializing default ChatClient");
        ChatClient defaultChatClient = dynamicModelService.getDefaultChatClient();
        if (defaultChatClient == null) {
            throw new IllegalStateException("Failed to initialize default ChatClient");
        }
        return defaultChatClient;
    }
    
    /**
     * 框架初始化完成后的回调
     */
    @Bean
    public SemanticMigrationInitializer semanticMigrationInitializer(
            ConfigurationManager configurationManager) {
        return new SemanticMigrationInitializer(configurationManager);
    }
    
    /**
     * 框架初始化器
     */
    public static class SemanticMigrationInitializer {
        
        private static final Logger logger = LoggerFactory.getLogger(SemanticMigrationInitializer.class);
        private final ConfigurationManager configurationManager;
        
        public SemanticMigrationInitializer(ConfigurationManager configurationManager) {
            this.configurationManager = configurationManager;
            initialize();
        }
        
        private void initialize() {
            logger.info("Initializing Semantic Migration Framework...");
            
            // 验证配置
            var validationResults = configurationManager.validateConfigurations();
            logger.info("Configuration validation results: {}", validationResults);
            
            // 打印配置统计
            var stats = configurationManager.getConfigurationStats();
            logger.info("Configuration statistics: {}", stats);
            
            // 检查关键配置
            if (configurationManager.getLlmConfig() == null) {
                logger.warn("LLM configuration not found, some features may not work properly");
            }
            
            if (configurationManager.getAllBusinessConfigs().isEmpty()) {
                logger.warn("No business configurations found");
            } else {
                logger.info("Loaded business configurations: {}", 
                    configurationManager.getAllBusinessConfigs().keySet());
            }
            
            if (configurationManager.getAllPromptTemplates().isEmpty()) {
                logger.warn("No prompt templates found");
            } else {
                logger.info("Loaded prompt templates: {}", 
                    configurationManager.getAllPromptTemplates().keySet());
            }
            
            logger.info("Semantic Migration Framework initialized successfully");
        }
    }
}
