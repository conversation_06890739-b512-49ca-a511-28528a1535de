package com.phodal.semantic.config;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * LLM配置类，用于解析llm_config.json
 */
public class LlmConfig {
    
    @JsonProperty("chat_service_llm")
    private List<ChatServiceLlm> chatServiceLlm;
    
    public List<ChatServiceLlm> getChatServiceLlm() {
        return chatServiceLlm;
    }
    
    public void setChatServiceLlm(List<ChatServiceLlm> chatServiceLlm) {
        this.chatServiceLlm = chatServiceLlm;
    }
    
    /**
     * 获取默认的LLM配置（第一个）
     */
    public ChatServiceLlm getDefaultLlm() {
        if (chatServiceLlm != null && !chatServiceLlm.isEmpty()) {
            return chatServiceLlm.get(0);
        }
        return null;
    }
    
    public static class ChatServiceLlm {
        @JsonProperty("base_url")
        private String baseUrl;
        
        @JsonProperty("model_name")
        private String modelName;
        
        @JsonProperty("api_key")
        private String apiKey;
        
        public String getBaseUrl() {
            return baseUrl;
        }
        
        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }
        
        public String getModelName() {
            return modelName;
        }
        
        public void setModelName(String modelName) {
            this.modelName = modelName;
        }
        
        public String getApiKey() {
            return apiKey;
        }
        
        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }
    }
}
