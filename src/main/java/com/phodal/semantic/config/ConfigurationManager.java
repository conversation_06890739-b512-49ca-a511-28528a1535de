package com.phodal.semantic.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 配置管理器，负责加载和管理各种配置文件
 */
@Component
public class ConfigurationManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationManager.class);
    private final ObjectMapper objectMapper;
    private final Map<String, BusinessConfig> businessConfigs = new ConcurrentHashMap<>();
    private final Map<String, String> promptTemplates = new ConcurrentHashMap<>();
    private LlmConfig llmConfig;
    
    public ConfigurationManager() {
        this.objectMapper = new ObjectMapper();
        initializeConfigurations();
    }
    
    /**
     * 初始化所有配置
     */
    private void initializeConfigurations() {
        loadLlmConfig();
        loadBusinessConfigs();
        loadPromptTemplates();
    }
    
    /**
     * 加载 LLM 配置
     */
    private void loadLlmConfig() {
        try {
            ClassPathResource resource = new ClassPathResource("llm_config.json");
            try (InputStream inputStream = resource.getInputStream()) {
                this.llmConfig = objectMapper.readValue(inputStream, LlmConfig.class);
                logger.info("LLM configuration loaded successfully");
            }
        } catch (IOException e) {
            logger.error("Failed to load LLM configuration", e);
        }
    }
    
    /**
     * 加载所有业务配置
     */
    private void loadBusinessConfigs() {
        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources("classpath*:*/config.json");
            
            for (Resource resource : resources) {
                String path = resource.getURL().getPath();
                String businessName = extractBusinessNameFromPath(path);
                
                if (businessName != null) {
                    try (InputStream inputStream = resource.getInputStream()) {
                        BusinessConfig config = objectMapper.readValue(inputStream, BusinessConfig.class);
                        businessConfigs.put(businessName, config);
                        logger.info("Business configuration loaded for: {}", businessName);
                    }
                }
            }
            
            logger.info("Loaded {} business configurations", businessConfigs.size());
        } catch (IOException e) {
            logger.error("Failed to load business configurations", e);
        }
    }
    
    /**
     * 加载所有提示词模板
     */
    private void loadPromptTemplates() {
        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources("classpath*:*/skprompt.txt");
            
            for (Resource resource : resources) {
                String path = resource.getURL().getPath();
                String businessName = extractBusinessNameFromPath(path);
                
                if (businessName != null) {
                    try (InputStream inputStream = resource.getInputStream()) {
                        String template = new String(inputStream.readAllBytes());
                        promptTemplates.put(businessName, template);
                        logger.info("Prompt template loaded for: {}", businessName);
                    }
                }
            }
            
            logger.info("Loaded {} prompt templates", promptTemplates.size());
        } catch (IOException e) {
            logger.error("Failed to load prompt templates", e);
        }
    }
    
    /**
     * 从路径中提取业务名称
     */
    private String extractBusinessNameFromPath(String path) {
        // 从路径中提取业务名称，例如：/path/to/compliance_review/config.json -> compliance_review
        String[] parts = path.split("/");
        for (int i = parts.length - 2; i >= 0; i--) {
            if (!parts[i].isEmpty() && !parts[i].equals("classes") && !parts[i].equals("resources")) {
                return parts[i];
            }
        }
        return null;
    }
    
    /**
     * 获取 LLM 配置
     */
    public LlmConfig getLlmConfig() {
        return llmConfig;
    }
    
    /**
     * 获取业务配置
     */
    public BusinessConfig getBusinessConfig(String businessName) {
        return businessConfigs.get(businessName);
    }
    
    /**
     * 获取所有业务配置
     */
    public Map<String, BusinessConfig> getAllBusinessConfigs() {
        return new HashMap<>(businessConfigs);
    }
    
    /**
     * 获取提示词模板
     */
    public String getPromptTemplate(String businessName) {
        return promptTemplates.get(businessName);
    }
    
    /**
     * 获取所有提示词模板
     */
    public Map<String, String> getAllPromptTemplates() {
        return new HashMap<>(promptTemplates);
    }
    
    /**
     * 重新加载所有配置
     */
    public void reloadConfigurations() {
        businessConfigs.clear();
        promptTemplates.clear();
        initializeConfigurations();
        logger.info("All configurations reloaded");
    }
    
    /**
     * 重新加载特定业务的配置
     */
    public void reloadBusinessConfiguration(String businessName) {
        try {
            // 重新加载业务配置
            ClassPathResource configResource = new ClassPathResource(businessName + "/config.json");
            if (configResource.exists()) {
                try (InputStream inputStream = configResource.getInputStream()) {
                    BusinessConfig config = objectMapper.readValue(inputStream, BusinessConfig.class);
                    businessConfigs.put(businessName, config);
                    logger.info("Business configuration reloaded for: {}", businessName);
                }
            }
            
            // 重新加载提示词模板
            ClassPathResource promptResource = new ClassPathResource(businessName + "/skprompt.txt");
            if (promptResource.exists()) {
                try (InputStream inputStream = promptResource.getInputStream()) {
                    String template = new String(inputStream.readAllBytes());
                    promptTemplates.put(businessName, template);
                    logger.info("Prompt template reloaded for: {}", businessName);
                }
            }
            
        } catch (IOException e) {
            logger.error("Failed to reload configuration for business: {}", businessName, e);
        }
    }
    
    /**
     * 获取配置统计信息
     */
    public Map<String, Object> getConfigurationStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("llmConfigLoaded", llmConfig != null);
        stats.put("businessConfigCount", businessConfigs.size());
        stats.put("promptTemplateCount", promptTemplates.size());
        stats.put("businessNames", businessConfigs.keySet());
        return stats;
    }
    
    /**
     * 验证配置完整性
     */
    public Map<String, Boolean> validateConfigurations() {
        Map<String, Boolean> validation = new HashMap<>();
        
        // 验证 LLM 配置
        validation.put("llmConfig", llmConfig != null && llmConfig.getDefaultLlm() != null);
        
        // 验证每个业务配置
        for (String businessName : businessConfigs.keySet()) {
            BusinessConfig config = businessConfigs.get(businessName);
            boolean isValid = config != null && 
                             config.getExecutionSettings() != null && 
                             config.getExecutionSettings().containsKey("default");
            validation.put("businessConfig_" + businessName, isValid);
        }
        
        return validation;
    }
}
