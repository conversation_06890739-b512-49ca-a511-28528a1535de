package com.phodal.semantic.config;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * 业务配置类，用于解析各业务模块的 config.json
 */
public class BusinessConfig {
    
    private int schema;
    private String type;
    private String description;
    
    @JsonProperty("execution_settings")
    private Map<String, ExecutionSettings> executionSettings;
    
    @JsonProperty("input_variables")
    private List<InputVariable> inputVariables;
    
    public int getSchema() {
        return schema;
    }
    
    public void setSchema(int schema) {
        this.schema = schema;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Map<String, ExecutionSettings> getExecutionSettings() {
        return executionSettings;
    }
    
    public void setExecutionSettings(Map<String, ExecutionSettings> executionSettings) {
        this.executionSettings = executionSettings;
    }
    
    public List<InputVariable> getInputVariables() {
        return inputVariables;
    }
    
    public void setInputVariables(List<InputVariable> inputVariables) {
        this.inputVariables = inputVariables;
    }
    
    /**
     * 执行设置类
     */
    public static class ExecutionSettings {
        @JsonProperty("ai_model_id")
        private String aiModelId;
        
        @JsonProperty("max_tokens")
        private Integer maxTokens;
        
        private Double temperature;
        
        @JsonProperty("top_p")
        private Double topP;
        
        @JsonProperty("presence_penalty")
        private Double presencePenalty;
        
        @JsonProperty("frequency_penalty")
        private Double frequencyPenalty;
        
        public String getAiModelId() {
            return aiModelId;
        }
        
        public void setAiModelId(String aiModelId) {
            this.aiModelId = aiModelId;
        }
        
        public Integer getMaxTokens() {
            return maxTokens;
        }
        
        public void setMaxTokens(Integer maxTokens) {
            this.maxTokens = maxTokens;
        }
        
        public Double getTemperature() {
            return temperature;
        }
        
        public void setTemperature(Double temperature) {
            this.temperature = temperature;
        }
        
        public Double getTopP() {
            return topP;
        }
        
        public void setTopP(Double topP) {
            this.topP = topP;
        }
        
        public Double getPresencePenalty() {
            return presencePenalty;
        }
        
        public void setPresencePenalty(Double presencePenalty) {
            this.presencePenalty = presencePenalty;
        }
        
        public Double getFrequencyPenalty() {
            return frequencyPenalty;
        }
        
        public void setFrequencyPenalty(Double frequencyPenalty) {
            this.frequencyPenalty = frequencyPenalty;
        }
    }
    
    /**
     * 输入变量类
     */
    public static class InputVariable {
        private String name;
        private String description;
        
        @JsonProperty("default")
        private String defaultValue;
        
        @JsonProperty("is_required")
        private Boolean isRequired;
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public String getDefaultValue() {
            return defaultValue;
        }
        
        public void setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
        }
        
        public Boolean getIsRequired() {
            return isRequired;
        }
        
        public void setIsRequired(Boolean isRequired) {
            this.isRequired = isRequired;
        }
    }
}
