package com.phodal.semantic.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 规划任务响应对象
 */
public class PlanTaskResponse {
    
    @JsonProperty("code")
    private int code;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("data")
    private PlanTaskData data;
    
    public PlanTaskResponse() {}
    
    public PlanTaskResponse(int code, String message, PlanTaskData data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public PlanTaskData getData() {
        return data;
    }
    
    public void setData(PlanTaskData data) {
        this.data = data;
    }
    
    /**
     * 创建成功响应
     */
    public static PlanTaskResponse success(List<PlanInfo> plans, String answer) {
        PlanTaskData data = new PlanTaskData();
        data.setPlans(plans);
        data.setAnswer(answer);
        return new PlanTaskResponse(0, "success", data);
    }
    
    /**
     * 创建错误响应
     */
    public static PlanTaskResponse error(String errorMessage) {
        return new PlanTaskResponse(-1, errorMessage, null);
    }
    
    /**
     * 规划任务数据对象
     */
    public static class PlanTaskData {
        
        @JsonProperty("plans")
        private List<PlanInfo> plans;
        
        @JsonProperty("answer")
        private String answer;
        
        public PlanTaskData() {}
        
        public List<PlanInfo> getPlans() {
            return plans;
        }
        
        public void setPlans(List<PlanInfo> plans) {
            this.plans = plans;
        }
        
        public String getAnswer() {
            return answer;
        }
        
        public void setAnswer(String answer) {
            this.answer = answer;
        }
        
        @Override
        public String toString() {
            return "PlanTaskData{" +
                    "plans=" + plans +
                    ", answer='" + answer + '\'' +
                    '}';
        }
    }
    
    @Override
    public String toString() {
        return "PlanTaskResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
