package com.phodal.semantic.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * 规划信息对象，表示单个执行计划
 */
public class PlanInfo {
    
    @JsonProperty("plugin_name")
    private String pluginName;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("variables")
    private Map<String, Object> variables;
    
    public PlanInfo() {}
    
    public PlanInfo(String pluginName, String name, String description, Map<String, Object> variables) {
        this.pluginName = pluginName;
        this.name = name;
        this.description = description;
        this.variables = variables;
    }
    
    public String getPluginName() {
        return pluginName;
    }
    
    public void setPluginName(String pluginName) {
        this.pluginName = pluginName;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Map<String, Object> getVariables() {
        return variables;
    }
    
    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }
    
    @Override
    public String toString() {
        return "PlanInfo{" +
                "pluginName='" + pluginName + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", variables=" + variables +
                '}';
    }
}
