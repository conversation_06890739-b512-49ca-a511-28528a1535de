package com.phodal.semantic.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * 规划任务请求对象
 */
public class PlanTaskRequest {
    
    @JsonProperty("messages")
    private List<Message> messages;
    
    @JsonProperty("juzi_data")
    private Map<String, Object> juziData;
    
    @JsonProperty("business_type")
    private String businessType;
    
    @JsonProperty("system_prompt")
    private String systemPrompt;
    
    public PlanTaskRequest() {}
    
    public List<Message> getMessages() {
        return messages;
    }
    
    public void setMessages(List<Message> messages) {
        this.messages = messages;
    }
    
    public Map<String, Object> getJuziData() {
        return juziData;
    }
    
    public void setJuziData(Map<String, Object> juziData) {
        this.juziData = juziData;
    }
    
    public String getBusinessType() {
        return businessType;
    }
    
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    
    public String getSystemPrompt() {
        return systemPrompt;
    }
    
    public void setSystemPrompt(String systemPrompt) {
        this.systemPrompt = systemPrompt;
    }
    
    /**
     * 获取最后一条用户消息的内容
     */
    public String getLastUserMessage() {
        if (messages == null || messages.isEmpty()) {
            return null;
        }
        
        // 从后往前查找最后一条用户消息
        for (int i = messages.size() - 1; i >= 0; i--) {
            Message message = messages.get(i);
            if ("user".equals(message.getRole())) {
                return message.getContent();
            }
        }
        
        return null;
    }
    
    /**
     * 获取聊天ID
     */
    public String getChatId() {
        if (juziData != null && juziData.containsKey("chatId")) {
            return String.valueOf(juziData.get("chatId"));
        }
        return null;
    }
    
    @Override
    public String toString() {
        return "PlanTaskRequest{" +
                "messages=" + messages +
                ", juziData=" + juziData +
                ", businessType='" + businessType + '\'' +
                ", systemPrompt='" + systemPrompt + '\'' +
                '}';
    }
}
