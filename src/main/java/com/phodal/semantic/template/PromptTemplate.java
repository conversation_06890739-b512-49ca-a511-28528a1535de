package com.phodal.semantic.template;

import java.util.Map;

/**
 * 提示词模板接口，用于渲染包含变量的模板字符串
 */
public interface PromptTemplate {
    
    /**
     * 渲染模板，将变量替换为实际值
     * 
     * @param variables 变量映射
     * @return 渲染后的字符串
     */
    String render(Map<String, Object> variables);
    
    /**
     * 获取模板中的所有变量名
     * 
     * @return 变量名集合
     */
    java.util.Set<String> getVariableNames();
    
    /**
     * 获取原始模板字符串
     * 
     * @return 原始模板
     */
    String getTemplate();
}
