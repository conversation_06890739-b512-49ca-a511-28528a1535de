package com.phodal.semantic.template;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Semantic Kernel 风格的提示词模板实现
 * 支持 {{$variableName}} 和 {{ $variableName }} 格式的变量替换
 */
public class SemanticKernelPromptTemplate implements PromptTemplate {
    
    private static final Logger logger = LoggerFactory.getLogger(SemanticKernelPromptTemplate.class);
    
    // 匹配 {{$variableName}} 或 {{ $variableName }} 格式的变量
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\{\\{\\s*\\$([a-zA-Z0-9_]+)\\s*\\}\\}");
    
    // 匹配 {{ActionPlanner_Excluded.ListOfFunctions}} 格式的特殊变量
    private static final Pattern SPECIAL_VARIABLE_PATTERN = Pattern.compile("\\{\\{\\s*([a-zA-Z0-9_]+\\.[a-zA-Z0-9_]+)\\s*\\}\\}");
    
    private final String template;
    private final Set<String> variableNames;
    
    public SemanticKernelPromptTemplate(String template) {
        this.template = template;
        this.variableNames = extractVariableNames(template);
    }
    
    @Override
    public String render(Map<String, Object> variables) {
        if (template == null || template.isEmpty()) {
            return "";
        }

        if (variables == null) {
            variables = new HashMap<>();
        }

        String result = template;
        
        // 处理普通变量 {{$variableName}}
        Matcher matcher = VARIABLE_PATTERN.matcher(result);
        while (matcher.find()) {
            String variableName = matcher.group(1);
            String placeholder = matcher.group(0);
            
            Object value = variables.get(variableName);
            String replacement = value != null ? value.toString() : "";
            
            result = result.replace(placeholder, replacement);
            
            logger.debug("Replaced variable {} with value: {}", variableName, replacement);
        }
        
        // 处理特殊变量 {{ActionPlanner_Excluded.ListOfFunctions}}
        Matcher specialMatcher = SPECIAL_VARIABLE_PATTERN.matcher(result);
        while (specialMatcher.find()) {
            String variableName = specialMatcher.group(1);
            String placeholder = specialMatcher.group(0);
            
            Object value = variables.get(variableName);
            String replacement = value != null ? value.toString() : "";
            
            result = result.replace(placeholder, replacement);
            
            logger.debug("Replaced special variable {} with value: {}", variableName, replacement);
        }
        
        return result;
    }
    
    @Override
    public Set<String> getVariableNames() {
        return new HashSet<>(variableNames);
    }
    
    @Override
    public String getTemplate() {
        return template;
    }
    
    /**
     * 从模板中提取所有变量名
     */
    private Set<String> extractVariableNames(String template) {
        Set<String> names = new HashSet<>();
        
        if (template == null || template.isEmpty()) {
            return names;
        }
        
        // 提取普通变量
        Matcher matcher = VARIABLE_PATTERN.matcher(template);
        while (matcher.find()) {
            names.add(matcher.group(1));
        }
        
        // 提取特殊变量
        Matcher specialMatcher = SPECIAL_VARIABLE_PATTERN.matcher(template);
        while (specialMatcher.find()) {
            names.add(specialMatcher.group(1));
        }
        
        return names;
    }
    
    /**
     * 创建模板实例的工厂方法
     */
    public static SemanticKernelPromptTemplate create(String template) {
        return new SemanticKernelPromptTemplate(template);
    }
    
    /**
     * 从资源文件加载模板
     */
    public static SemanticKernelPromptTemplate fromResource(String resourcePath) {
        try {
            org.springframework.core.io.ClassPathResource resource = 
                new org.springframework.core.io.ClassPathResource(resourcePath);
            String template = resource.getContentAsString(java.nio.charset.StandardCharsets.UTF_8);
            return new SemanticKernelPromptTemplate(template);
        } catch (Exception e) {
            logger.error("Failed to load template from resource: {}", resourcePath, e);
            throw new RuntimeException("Failed to load template from resource: " + resourcePath, e);
        }
    }
    
    /**
     * 验证模板语法是否正确
     */
    public boolean isValid() {
        if (template == null) {
            return false;
        }
        
        try {
            // 检查大括号是否匹配
            int openCount = 0;
            int closeCount = 0;
            
            for (int i = 0; i < template.length() - 1; i++) {
                if (template.charAt(i) == '{' && template.charAt(i + 1) == '{') {
                    openCount++;
                    i++; // 跳过下一个字符
                } else if (template.charAt(i) == '}' && template.charAt(i + 1) == '}') {
                    closeCount++;
                    i++; // 跳过下一个字符
                }
            }
            
            return openCount == closeCount;
        } catch (Exception e) {
            logger.error("Template validation failed", e);
            return false;
        }
    }
    
    @Override
    public String toString() {
        return "SemanticKernelPromptTemplate{" +
                "variableNames=" + variableNames +
                ", templateLength=" + (template != null ? template.length() : 0) +
                '}';
    }
}
