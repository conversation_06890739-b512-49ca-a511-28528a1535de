package com.phodal.semantic.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phodal.semantic.config.BusinessConfig;
import com.phodal.semantic.config.LlmConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 动态模型服务，支持根据不同业务配置创建对应的 ChatClient 实例
 */
@Service
public class DynamicModelService {

    private static final Logger logger = LoggerFactory.getLogger(DynamicModelService.class);
    private final ObjectMapper objectMapper;
    private final Map<String, ChatClient> chatClientCache = new ConcurrentHashMap<>();
    private ChatClient defaultChatClient;

    public DynamicModelService() {
        this.objectMapper = new ObjectMapper();
        initializeDefaultChatClient();
    }

    /**
     * 初始化默认的 ChatClient，使用 llm_config.json
     */
    private void initializeDefaultChatClient() {
        try {
            LlmConfig llmConfig = loadLlmConfig("llm_config.json");
            LlmConfig.ChatServiceLlm defaultLlm = llmConfig.getDefaultLlm();

            if (defaultLlm != null) {
                this.defaultChatClient = createChatClient(defaultLlm);
                logger.info("Default ChatClient initialized with model: {}", defaultLlm.getModelName());
            } else {
                logger.warn("No default LLM configuration found in llm_config.json");
            }
        } catch (Exception e) {
            logger.error("Failed to initialize default ChatClient", e);
        }
    }

    /**
     * 获取默认的 ChatClient
     */
    public ChatClient getDefaultChatClient() {
        return defaultChatClient;
    }

    /**
     * 根据业务名称获取对应的 ChatClient
     * @param businessName 业务名称，对应 resources 下的目录名
     * @return ChatClient 实例
     */
    public ChatClient getChatClient(String businessName) {
        return chatClientCache.computeIfAbsent(businessName, this::createChatClientForBusiness);
    }

    /**
     * 为特定业务创建 ChatClient
     */
    private ChatClient createChatClientForBusiness(String businessName) {
        try {
            // 尝试加载业务特定的配置
            String configPath = businessName + "/config.json";
            BusinessConfig businessConfig = loadBusinessConfig(configPath);

            if (businessConfig != null && businessConfig.getExecutionSettings() != null) {
                // 使用业务配置创建 ChatClient
                return createChatClientFromBusinessConfig(businessConfig);
            } else {
                logger.info("No specific config found for business: {}, using default ChatClient", businessName);
                return defaultChatClient;
            }
        } catch (Exception e) {
            logger.warn("Failed to create ChatClient for business: {}, falling back to default", businessName, e);
            return defaultChatClient;
        }
    }

    /**
     * 从 LLM 配置创建 ChatClient
     */
    private ChatClient createChatClient(LlmConfig.ChatServiceLlm llmConfig) {
        try {
            // 创建 OpenAI API 实例
            OpenAiApi openAiApi = OpenAiApi.builder()
                .baseUrl(llmConfig.getBaseUrl())
                .apiKey(llmConfig.getApiKey())
                .build();

            // 创建 ChatModel
            OpenAiChatModel chatModel = OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(OpenAiChatOptions.builder()
                    .model(llmConfig.getModelName())
                    .temperature(0.7)
                    .build())
                .build();

            // 创建 ChatClient
            return ChatClient.create(chatModel);

        } catch (Exception e) {
            logger.error("Failed to create ChatClient with config: {}", llmConfig.getModelName(), e);
            throw new RuntimeException("Failed to create ChatClient", e);
        }
    }

    /**
     * 从业务配置创建 ChatClient
     */
    private ChatClient createChatClientFromBusinessConfig(BusinessConfig businessConfig) {
        try {
            BusinessConfig.ExecutionSettings defaultSettings = businessConfig.getExecutionSettings().get("default");
            if (defaultSettings == null) {
                logger.warn("No default execution settings found in business config");
                return defaultChatClient;
            }

            // 使用默认的 LLM 配置，但应用业务特定的参数
            LlmConfig llmConfig = loadLlmConfig("llm_config.json");
            LlmConfig.ChatServiceLlm defaultLlm = llmConfig.getDefaultLlm();

            if (defaultLlm == null) {
                return defaultChatClient;
            }

            // 创建 OpenAI API 实例
            OpenAiApi openAiApi = OpenAiApi.builder()
                .baseUrl(defaultLlm.getBaseUrl())
                .apiKey(defaultLlm.getApiKey())
                .build();

            // 创建带有业务特定配置的 ChatModel
            OpenAiChatModel chatModel = OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(OpenAiChatOptions.builder()
                    .model(defaultLlm.getModelName())
                    .temperature(defaultSettings.getTemperature())
                    .maxTokens(defaultSettings.getMaxTokens())
                    .topP(defaultSettings.getTopP())
                    .presencePenalty(defaultSettings.getPresencePenalty())
                    .frequencyPenalty(defaultSettings.getFrequencyPenalty())
                    .build())
                .build();

            return ChatClient.create(chatModel);

        } catch (Exception e) {
            logger.error("Failed to create ChatClient from business config", e);
            return defaultChatClient;
        }
    }

    /**
     * 加载 LLM 配置文件
     */
    private LlmConfig loadLlmConfig(String configPath) throws IOException {
        ClassPathResource resource = new ClassPathResource(configPath);
        try (InputStream inputStream = resource.getInputStream()) {
            return objectMapper.readValue(inputStream, LlmConfig.class);
        }
    }

    /**
     * 加载业务配置文件
     */
    private BusinessConfig loadBusinessConfig(String configPath) {
        try {
            ClassPathResource resource = new ClassPathResource(configPath);
            if (!resource.exists()) {
                logger.debug("Business config not found: {}", configPath);
                return null;
            }

            try (InputStream inputStream = resource.getInputStream()) {
                return objectMapper.readValue(inputStream, BusinessConfig.class);
            }
        } catch (IOException e) {
            logger.warn("Failed to load business config: {}", configPath, e);
            return null;
        }
    }

    /**
     * 清除缓存的 ChatClient
     */
    public void clearCache() {
        chatClientCache.clear();
        logger.info("ChatClient cache cleared");
    }

    /**
     * 获取缓存的 ChatClient 数量
     */
    public int getCacheSize() {
        return chatClientCache.size();
    }
}
