# Semantic Kernel 模板系统实现

## 概述

基于官方 Semantic Kernel Java 实现，我们重新设计了提示词模板处理系统，支持 `{{$variableName}}` 和 `{{ActionPlanner_Excluded.ListOfFunctions}}` 格式的变量替换。

## 核心组件

### 1. PromptTemplate 接口

```java
public interface PromptTemplate {
    String render(Map<String, Object> variables);
    Set<String> getVariableNames();
    String getTemplate();
}
```

定义了模板的基本操作：渲染、获取变量名、获取原始模板。

### 2. SemanticKernelPromptTemplate 实现

核心模板实现类，支持：

- **普通变量**: `{{$variableName}}` 或 `{{ $variableName }}`
- **特殊变量**: `{{ActionPlanner_Excluded.ListOfFunctions}}`
- **模板验证**: 检查大括号匹配
- **错误处理**: 优雅处理缺失变量和null值

#### 支持的变量格式

```java
// 普通变量（支持空格）
"{{$input}}"
"{{ $input }}"
"{{$userName}}"

// 特殊变量（点分隔）
"{{ActionPlanner_Excluded.ListOfFunctions}}"
"{{Config.MaxTokens}}"
```

### 3. PromptTemplateManager 管理器

提供模板的统一管理：

- **模板缓存**: 避免重复加载
- **工具列表生成**: 自动生成可用工具列表
- **业务类型支持**: 根据业务类型渲染不同模板
- **资源管理**: 从classpath加载模板文件

## 使用示例

### 基本用法

```java
// 创建模板
String templateStr = "Hello {{$name}}, welcome to {{$place}}!";
SemanticKernelPromptTemplate template = SemanticKernelPromptTemplate.create(templateStr);

// 准备变量
Map<String, Object> variables = new HashMap<>();
variables.put("name", "Alice");
variables.put("place", "Wonderland");

// 渲染模板
String result = template.render(variables);
// 输出: "Hello Alice, welcome to Wonderland!"
```

### 原始 skprompt.txt 模板处理

```java
// 原始模板内容
String originalTemplate = """
    你是一个智能交易助手，会根据用户最新的需求，每次选择最合适的1个函数。
    
    [实际场景开始]
    - 函数列表开始:
    {{ActionPlanner_Excluded.ListOfFunctions}}
    - 函数列表结束.
    [交互内容]
    {{ $input }}
    [返回体]
    """;

// 使用模板管理器渲染
PromptTemplateManager manager = new PromptTemplateManager(toolLoader);
String rendered = manager.renderPlanTemplate("plan", "用户输入内容");
```

### 在 PlannerService 中的集成

```java
@Service
public class PlannerService {
    private final PromptTemplateManager templateManager;
    
    public String plan(String input, String businessType) {
        // 使用模板管理器渲染系统提示词
        String systemPrompt = templateManager.renderPlanTemplate(businessType, input);
        
        // 调用 ChatClient
        String response = chatClient.prompt()
                .system(systemPrompt)
                .user(input)
                .toolCallbacks(availableTools)
                .call()
                .content();
        
        return response;
    }
}
```

## 模板变量替换机制

### 1. 变量识别

使用正则表达式识别两种类型的变量：

```java
// 普通变量: {{$variableName}}
private static final Pattern VARIABLE_PATTERN = 
    Pattern.compile("\\{\\{\\s*\\$([a-zA-Z0-9_]+)\\s*\\}\\}");

// 特殊变量: {{ActionPlanner_Excluded.ListOfFunctions}}
private static final Pattern SPECIAL_VARIABLE_PATTERN = 
    Pattern.compile("\\{\\{\\s*([a-zA-Z0-9_]+\\.[a-zA-Z0-9_]+)\\s*\\}\\}");
```

### 2. 变量替换

```java
public String render(Map<String, Object> variables) {
    String result = template;
    
    // 处理普通变量
    Matcher matcher = VARIABLE_PATTERN.matcher(result);
    while (matcher.find()) {
        String variableName = matcher.group(1);
        String placeholder = matcher.group(0);
        Object value = variables.get(variableName);
        String replacement = value != null ? value.toString() : "";
        result = result.replace(placeholder, replacement);
    }
    
    // 处理特殊变量
    // ... 类似处理
    
    return result;
}
```

### 3. 工具列表生成

```java
private String generateToolsList(String businessType) {
    List<ToolCallback> tools = toolLoader.getAllAvailableTools(businessType);
    StringBuilder toolsList = new StringBuilder();
    
    for (ToolCallback tool : tools) {
        String toolName = tool.getToolDefinition().name();
        String description = tool.getToolDefinition().description();
        
        toolsList.append("// ").append(description).append("\n");
        toolsList.append(toolName).append("\n");
        toolsList.append("请求参数 \"input\": 用户输入文本.\n\n");
    }
    
    return toolsList.toString().trim();
}
```

## 错误处理

### 1. 模板验证

```java
public boolean isValid() {
    // 检查大括号是否匹配
    int openCount = 0, closeCount = 0;
    
    for (int i = 0; i < template.length() - 1; i++) {
        if (template.charAt(i) == '{' && template.charAt(i + 1) == '{') {
            openCount++;
            i++;
        } else if (template.charAt(i) == '}' && template.charAt(i + 1) == '}') {
            closeCount++;
            i++;
        }
    }
    
    return openCount == closeCount;
}
```

### 2. 缺失变量处理

- 缺失的变量会被替换为空字符串
- null 变量映射会被转换为空的 HashMap
- 记录调试日志便于排查问题

## 性能优化

### 1. 模板缓存

```java
@Component
public class PromptTemplateManager {
    private final Map<String, PromptTemplate> templateCache = new ConcurrentHashMap<>();
    
    public PromptTemplate getTemplate(String templatePath) {
        return templateCache.computeIfAbsent(templatePath, this::loadTemplate);
    }
}
```

### 2. 延迟加载

- 模板只在首次使用时加载
- 工具列表动态生成，避免过期问题

## 测试覆盖

完整的测试套件包括：

- **基本变量替换测试**
- **特殊变量处理测试**
- **模板验证测试**
- **错误处理测试**
- **边界条件测试**

## 与官方实现的对比

| 特性 | 官方实现 | 我们的实现 |
|------|----------|------------|
| 变量格式 | `{{$var}}` | `{{$var}}` + `{{Module.Function}}` |
| 模板引擎 | Handlebars + 自定义 | 正则表达式 + 字符串替换 |
| 复杂度 | 高（支持循环、条件等） | 低（专注变量替换） |
| 性能 | 中等 | 高（简单直接） |
| 扩展性 | 强 | 中等 |
| 学习成本 | 高 | 低 |

## 部署和使用

1. **依赖注入**: PromptTemplateManager 自动注册为 Spring Bean
2. **配置**: 模板文件放在 `src/main/resources/` 下
3. **集成**: 在 PlannerService 中使用模板管理器
4. **监控**: 通过日志监控模板渲染过程

## 总结

新的模板系统成功实现了：

- ✅ **兼容性**: 完全支持原有 skprompt.txt 格式
- ✅ **性能**: 高效的变量替换机制
- ✅ **可维护性**: 清晰的接口设计和错误处理
- ✅ **扩展性**: 支持自定义模板和变量类型
- ✅ **测试覆盖**: 完整的单元测试和演示程序

这个实现为 Semantic Migration 项目提供了一个轻量级、高效的模板处理解决方案，完美替代了原有的静态字符串处理方式。
