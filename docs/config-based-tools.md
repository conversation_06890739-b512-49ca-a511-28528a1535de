# 配置生成工具功能

## 概述

配置生成工具功能允许您通过配置文件（而不是 Java 代码中的 @Tool 注解）来定义和生成 Spring AI 工具。这种方式提供了更大的灵活性，特别适合需要动态配置工具的场景。

## 功能特点

- **配置驱动**: 通过 `config.json` 和 `skprompt.txt` 文件定义工具
- **自动生成**: 自动生成 Spring AI 的 ToolCallback 实现
- **JSON Schema**: 自动生成输入参数的 JSON Schema
- **模板渲染**: 支持变量替换的提示词模板
- **无缝集成**: 与现有的 ToolLoader 系统完全集成

## 目录结构

配置生成的工具需要按以下目录结构组织：

```
src/main/resources/
├── context_qa/
│   ├── config.json      # 工具配置文件
│   └── skprompt.txt     # 提示词模板文件
└── other_business/
    ├── config.json
    └── skprompt.txt
```

## 配置文件格式

### config.json

```json
{
  "schema": 1,
  "type": "completion",
  "description": "工具的描述信息，用于 AI 模型理解何时调用此工具",
  "execution_settings": {
    "default": {
      "ai_model_id": "deepseek-chat",
      "max_tokens": 8192,
      "temperature": 0,
      "presence_penalty": 0.6,
      "frequency_penalty": 0.0
    }
  },
  "input_variables": [
    {
      "name": "input",
      "description": "输入参数的描述",
      "default": "",
      "is_required": true
    },
    {
      "name": "query",
      "description": "另一个输入参数的描述",
      "default": "",
      "is_required": true
    }
  ]
}
```

### skprompt.txt

```text
你是一个乐于助人的说中文的助手，擅长根据给出的文本，根据用户的需求进行相应的回答。
文本可能是一段自然语言文字，也可能是JSON格式。

文本开始：
{{$input}}

用户: {{$query}}
助手:
```

## 使用示例

### 1. 创建配置文件

创建 `src/main/resources/context_qa/` 目录，并添加上述配置文件。

### 2. 在代码中使用

```java
@Autowired
private ToolLoader toolLoader;

// 获取包含配置生成工具的所有工具
List<ToolCallback> tools = toolLoader.getAllAvailableTools("context_qa");

// 在 ChatClient 中使用
String response = ChatClient.create(chatModel)
    .prompt("根据以下信息回答问题...")
    .toolCallbacks(tools)
    .call()
    .content();
```

### 3. 工具调用

当 AI 模型决定调用工具时，会传递如下格式的 JSON：

```json
{
  "input": "这里是需要分析的文本内容",
  "query": "用户的具体问题"
}
```

## 实现原理

1. **ConfigBasedToolGenerator**: 负责从配置文件生成 ToolCallback
2. **ToolLoader 集成**: 自动检测和加载配置生成的工具
3. **模板渲染**: 使用简单的字符串替换来渲染提示词模板
4. **Schema 生成**: 根据 input_variables 自动生成 JSON Schema

## 优势

- **灵活性**: 无需修改 Java 代码即可添加新工具
- **可维护性**: 配置文件更容易维护和版本控制
- **动态性**: 支持运行时动态加载工具配置
- **标准化**: 统一的配置格式和工具接口

## 注意事项

- 配置文件必须是有效的 JSON 格式
- 提示词模板中的变量必须与 input_variables 中定义的参数匹配
- 工具名称（目录名）必须唯一
- 确保 ChatClient 已正确配置和注入

## 扩展

您可以通过以下方式扩展此功能：

1. 添加更多的模板变量处理逻辑
2. 支持更复杂的 JSON Schema 生成
3. 添加配置文件验证
4. 支持多语言提示词模板
