# Semantic Migration API 重新设计

## 概述

基于 `plan-request-response.txt` 的参考格式和 `plan-log.md` 的日志要求，我们重新设计了 SemanticController 的 API 输入输出格式，提供了更结构化的请求响应格式和详细的日志记录。

## 新增的 API 端点

### 1. 规划任务 API

**端点**: `POST /api/v1/plan_task`

**请求头**:
- `user-token`: 用户令牌（可选），用于日志标识和会话跟踪

**请求体**:
```json
{
  "messages": [
    {
      "role": "user|assistant",
      "content": "消息内容"
    }
  ],
  "juzi_data": {
    "chatId": "聊天ID",
    "其他键": "其他值"
  },
  "business_type": "业务类型（可选，默认为plan）",
  "system_prompt": "自定义系统提示词（可选）"
}
```

**响应体**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "plans": [
      {
        "plugin_name": "插件名称",
        "name": "函数名称",
        "description": "函数描述",
        "variables": {
          "input": "用户输入",
          "query": "查询内容"
        }
      }
    ],
    "answer": "AI生成的回答内容"
  }
}
```

## 新增的数据模型

### Message
表示对话中的单条消息：
- `role`: 角色（"user" 或 "assistant"）
- `content`: 消息内容

### PlanTaskRequest
规划任务请求对象：
- `messages`: 消息列表
- `juziData`: 橘子数据（包含chatId等信息）
- `businessType`: 业务类型
- `systemPrompt`: 自定义系统提示词

### PlanInfo
单个执行计划信息：
- `pluginName`: 插件名称
- `name`: 函数名称
- `description`: 函数描述
- `variables`: 函数参数

### PlanTaskResponse
规划任务响应对象：
- `code`: 响应码（0表示成功，-1表示错误）
- `message`: 响应消息
- `data`: 响应数据（包含plans和answer）

## 日志记录

新的API实现了详细的日志记录，模拟了原有Python系统的日志格式：

### 请求日志
```
messages=[Message(role='user', content='...')] juzi_data={'chatId': '123456'} user_token_here
```

### 规划过程日志
```
SemanticAgent-{userToken}:action_planner.py:create_plan_async()- Finding the best function for achieving the goal: 用户: {input}
SemanticAgent-{userToken}:action_planner.py:create_plan_async()- Plan generated by ActionPlanner: {json}
SemanticAgent-{userToken}:action_planner.py:create_plan_async()- Python dictionary of plan generated by ActionPlanner: {dict}
```

### 执行过程日志
```
SemanticAgent-{userToken}:plan.py:invoke_async()- {"is_finished": false, "type": "plan", "step": "action", "step_value": "{function}"}
SemanticAgent-{userToken}:plan.py:invoke_async()- {"is_finished": false, "type": "plan", "step": "action_input", "step_value": "{input}"}
SemanticAgent-{userToken}:plan.py:invoke_async()- {"is_finished": false, "type": "plan", "step": "action_output", "step_value": "{output}"}
```

### 性能日志
```
PLAN {planTime}
ANSWER {totalTime}
LASTPART {lastPartTime}
```

## 向后兼容性

为了保持向后兼容，原有的API端点仍然可用，但路径有所调整：

- `/api/semantic/plan` - 原有的规划API
- `/api/semantic/plan/custom` - 自定义提示词API
- `/api/semantic/tools` - 获取工具列表
- `/api/semantic/status` - 系统状态
- `/api/semantic/health` - 健康检查
- `/api/semantic/config/{businessType}` - 业务配置
- `/api/semantic/reload` - 重新加载配置

## 使用示例

### 基本请求
```bash
curl -X POST "http://localhost:8719/api/v1/plan_task" \
  -H "Content-Type: application/json" \
  -H "user-token: your_user_token_here" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "外代销产品，请问给代销机构下发数据一般是几点？"
      }
    ],
    "juzi_data": {
      "chatId": "123456",
      "other_key": "other_value"
    }
  }'
```

### 多轮对话请求
```bash
curl -X POST "http://localhost:8719/api/v1/plan_task" \
  -H "Content-Type: application/json" \
  -H "user-token: conversation_user" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "今天天气怎么样？"
      },
      {
        "role": "assistant", 
        "content": "抱歉，我无法获取实时天气信息。"
      },
      {
        "role": "user",
        "content": "那请帮我分析一下股市行情"
      }
    ],
    "juzi_data": {
      "chatId": "multi_turn_123"
    },
    "business_type": "qa"
  }'
```

## 测试

项目包含了完整的单元测试，验证了：
- 正常的规划任务处理
- 错误处理机制
- 用户令牌的可选性
- 响应格式的正确性

运行测试：
```bash
./mvnw test -Dtest=SemanticControllerTest
```

## 部署注意事项

1. 确保所有依赖的DTO类都在classpath中
2. 验证Jackson配置正确处理JSON序列化/反序列化
3. 检查日志配置以确保详细日志正确输出
4. 测试新旧API端点的兼容性
