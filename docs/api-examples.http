### 新版本的规划任务API示例

# 基本的规划任务请求
POST http://localhost:8719/api/v1/plan_task
user-token: your_user_token_here
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "外代销产品，请问给代销机构下发数据一般是几点？"
    }
  ],
  "juzi_data": {
    "chatId": "123456",
    "other_key": "other_value"
  }
}

###

# 带有业务类型的规划任务请求
POST http://localhost:8719/api/v1/plan_task
user-token: test_user_123
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "请帮我分析一下今天的市场行情"
    }
  ],
  "juzi_data": {
    "chatId": "789012",
    "sessionId": "session_abc"
  },
  "business_type": "qa"
}

###

# 带有自定义系统提示词的请求
POST http://localhost:8719/api/v1/plan_task
user-token: advanced_user_456
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "询：600105永鼎股份，虚值5%看涨，2个月，300万名义本金"
    }
  ],
  "juzi_data": {
    "chatId": "345678",
    "tradeType": "option_inquiry"
  },
  "business_type": "plan",
  "system_prompt": "你是一个专业的期权交易助手，请根据用户的询价需求选择合适的工具进行处理。"
}

###

# 多轮对话示例
POST http://localhost:8719/api/v1/plan_task
user-token: conversation_user_789
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "产品强制赎回流程申请里的折扣率是指什么？"
    },
    {
      "role": "assistant",
      "content": "赎回费的折扣率"
    },
    {
      "role": "user",
      "content": "今天是多少号？"
    },
    {
      "role": "assistant",
      "content": "对不起，我暂时还没掌握这项技能"
    },
    {
      "role": "user",
      "content": "询：600105永鼎股份，虚值5%看涨，2个月，300万名义本金\n中国平安，1m/2m，200万，9070，询价"
    }
  ],
  "juzi_data": {
    "chatId": "multi_turn_123",
    "conversationId": "conv_456"
  }
}

###

### 向后兼容的API示例

# 原有的规划API（保持兼容）
POST http://localhost:8719/api/semantic/plan
Content-Type: application/json

{
  "input": "请帮我规划一下今天的任务",
  "businessType": "plan"
}

###

# 自定义提示词API
POST http://localhost:8719/api/semantic/plan/custom
Content-Type: application/json

{
  "input": "分析市场趋势",
  "businessType": "qa",
  "systemPrompt": "你是一个专业的金融分析师，请提供详细的市场分析。"
}

###

# 获取可用工具
GET http://localhost:8719/api/semantic/tools?businessType=plan

###

# 系统状态检查
GET http://localhost:8719/api/semantic/status

###

# 健康检查
GET http://localhost:8719/api/semantic/health

###

# 获取业务配置
GET http://localhost:8719/api/semantic/config/plan

###

# 重新加载配置
POST http://localhost:8719/api/semantic/reload?businessType=plan
